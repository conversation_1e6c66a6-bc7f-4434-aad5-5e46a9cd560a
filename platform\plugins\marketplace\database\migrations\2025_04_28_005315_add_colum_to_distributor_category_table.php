<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    public function up(): void
    {
        Schema::table('mp_distributor_categories', function (Blueprint $table): void {
            $table->string('icon')->nullable();
            $table->string('icon_image')->nullable();
            $table->string('image')->nullable();
            $table->tinyInteger('is_featured')->default(0);
        });
    }
    public function down(): void
    {
        Schema::table('mp_distributor_categories', function (Blueprint $table): void {
            $table->dropColumn(['icon', 'icon_image', 'image', 'is_featured']);
        });
    }
};

