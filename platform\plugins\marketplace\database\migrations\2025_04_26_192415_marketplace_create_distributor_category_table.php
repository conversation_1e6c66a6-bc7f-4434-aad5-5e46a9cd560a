<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (! Schema::hasTable('mp_distributor_categories')) {
            Schema::create('mp_distributor_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name', 255);
                $table->foreignId('parent_id')->default(0);
                $table->string('status', 60)->default('published');
                $table->timestamps();
            });
        }

        if (! Schema::hasTable('mp_distributor_categories_translations')) {
            Schema::create('mp_distributor_categories_translations', function (Blueprint $table) {
                $table->string('lang_code');
                $table->foreignId('mp_distributor_categories_id');
                $table->string('name', 255)->nullable();

                $table->primary(['lang_code', 'mp_distributor_categories_id'], 'mp_distributor_categories_translations_primary');
            });
        }
        // Schema::create('mp_distributor_category_categories', function (Blueprint $table) {
        //     $table->foreignId('category_id')->index();
        //     $table->foreignId('distributor_id')->index();
        // });

    }

    public function down(): void
    {
        Schema::dropIfExists('mp_distributor_categories');
        Schema::dropIfExists('mp_distributor_categories_translations');
        // Schema::dropIfExists('mp_distributor_category_categories');
    }
};
