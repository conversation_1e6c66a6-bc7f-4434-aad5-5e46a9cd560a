<?php

namespace Botble\Marketplace\Exporters;

use Botble\DataSynchronize\Exporter\ExportColumn;
use <PERSON><PERSON>ble\DataSynchronize\Exporter\ExportCounter;
use Botble\DataSynchronize\Exporter\Exporter;
use Botble\Marketplace\Enums\StoreStatusEnum;
use Botble\Marketplace\Models\Store;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class ToolsStoreExporter extends Exporter
{
    public function getLabel(): string
    {
        return trans('plugins/marketplace::store.stores');
    }

    public function columns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),

            ExportColumn::make('name')
                ->label('Store Name'),

            ExportColumn::make('email')
                ->label('Email'),

            ExportColumn::make('phone')
                ->label('Phone'),

            ExportColumn::make('address')
                ->label('Address'),

            ExportColumn::make('country')
                ->label('Country'),

            ExportColumn::make('state')
                ->label('State'),

            ExportColumn::make('city')
                ->label('City'),

            ExportColumn::make('zip_code')
                ->label('Zip Code'),

            ExportColumn::make('company')
                ->label('Company'),

            ExportColumn::make('tax_id')
                ->label('Tax ID'),

            ExportColumn::make('description')
                ->label('Description'),

            ExportColumn::make('content')
                ->label('Content'),

            ExportColumn::make('logo')
                ->label('Logo'),

            ExportColumn::make('logo_square')
                ->label('Logo Square'),

            ExportColumn::make('cover_image')
                ->label('Cover Image'),

            ExportColumn::make('certificate_file')
                ->label('Certificate File'),

            ExportColumn::make('government_id_file')
                ->label('Government ID File'),

            ExportColumn::make('status')
                ->label('Status')
                ->dropdown(StoreStatusEnum::values()),

            ExportColumn::make('customer_id')
                ->label('Customer ID'),

            ExportColumn::make('customer_name')
                ->label('Customer Name'),

            ExportColumn::make('customer_email')
                ->label('Customer Email'),

            ExportColumn::make('created_at')
                ->label('Created At')
                ->dateTime(),

            ExportColumn::make('updated_at')
                ->label('Updated At')
                ->dateTime(),
        ];
    }

    public function counters(): array
    {
        $query = $this->getStoreQuery();

        return [
            ExportCounter::make()
                ->label(trans('plugins/marketplace::store.export.total_stores'))
                ->value(number_format($query->count())),
        ];
    }

    public function hasDataToExport(): bool
    {
        return $this->getStoreQuery()->exists();
    }

    public function collection(): Collection
    {
        return $this->getStoreQuery()
            ->with(['customer'])
            ->get()
            ->transform(function (Store $store) {
                return [
                    'id' => $store->id,
                    'name' => $store->name,
                    'email' => $store->email,
                    'phone' => $store->phone,
                    'address' => $store->address,
                    'country' => $store->country,
                    'state' => $store->state,
                    'city' => $store->city,
                    'zip_code' => $store->zip_code,
                    'company' => $store->company,
                    'tax_id' => $store->tax_id,
                    'description' => $store->description,
                    'content' => $store->content,
                    'logo' => $store->logo,
                    'logo_square' => $store->logo_square,
                    'cover_image' => $store->cover_image,
                    'certificate_file' => $store->certificate_file,
                    'government_id_file' => $store->government_id_file,
                    'status' => $store->status->value,
                    'customer_id' => $store->customer_id,
                    'customer_name' => $store->customer->name ?? '',
                    'customer_email' => $store->customer->email ?? '',
                    'created_at' => $store->created_at,
                    'updated_at' => $store->updated_at,
                ];
            });
    }

    protected function getStoreQuery(): Builder
    {
        return Store::query();
    }
}
