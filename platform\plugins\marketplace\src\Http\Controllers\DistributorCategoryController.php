<?php

namespace Bo<PERSON>ble\Marketplace\Http\Controllers;

use Botble\Base\Http\Actions\DeleteResourceAction;
use Botble\Marketplace\Http\Requests\DistributorCategoryRequest;
use Botble\Marketplace\Models\DistributorCategory;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Marketplace\Tables\DistributorCategoryTable;
use Botble\Marketplace\Forms\DistributorCategoryForm;

class DistributorCategoryController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans(trans('plugins/marketplace::distributor-category.name')), route('marketplace.distributor-category.index'));
    }

    public function index(DistributorCategoryTable $table)
    {
        $this->pageTitle(trans('plugins/marketplace::distributor-category.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/marketplace::distributor-category.create'));

        return DistributorCategoryForm::create()->renderForm();
    }

    public function store(DistributorCategoryRequest $request)
    {
        $form = DistributorCategoryForm::create()->setRequest($request);

        $form->save();

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.distributor-category.index'))
            ->setNextUrl(route('marketplace.distributor-category.edit', $form->getModel()->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(DistributorCategory $distributorCategory)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $distributorCategory->name]));

        return DistributorCategoryForm::createFromModel($distributorCategory)->renderForm();
    }

    public function update(DistributorCategory $distributorCategory, DistributorCategoryRequest $request)
    {
        DistributorCategoryForm::createFromModel($distributorCategory)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('marketplace.distributor-category.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(DistributorCategory $distributorCategory)
    {
        return DeleteResourceAction::make($distributorCategory);
    }
}
