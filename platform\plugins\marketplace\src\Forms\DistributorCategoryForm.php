<?php

namespace Bo<PERSON>ble\Marketplace\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\MediaImageFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\MediaImageField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Marketplace\Http\Requests\DistributorCategoryRequest;
use Botble\Marketplace\Models\DistributorCategory;

class DistributorCategoryForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(DistributorCategory::class)
            ->setValidatorClass(DistributorCategoryRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add(
                'icon_image',
                MediaImageField::class,
                MediaImageFieldOption::make()
                    ->label(__('Icon'))
            )
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
