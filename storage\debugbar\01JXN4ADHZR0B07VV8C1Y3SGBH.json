{"__meta": {"id": "01JXN4ADHZR0B07VV8C1Y3SGBH", "datetime": "2025-06-13 17:10:10", "utime": **********.240437, "method": "POST", "uri": "/admin/marketplaces/stores/import/import", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.060041, "end": **********.240472, "duration": 10.180431127548218, "duration_str": "10.18s", "measures": [{"label": "Booting", "start": **********.060041, "relative_start": 0, "end": **********.828816, "relative_end": **********.828816, "duration": 0.****************, "duration_str": "769ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.82883, "relative_start": 0.****************, "end": **********.240476, "relative_end": 3.814697265625e-06, "duration": 9.***************, "duration_str": "9.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.853992, "relative_start": 0.****************, "end": **********.865352, "relative_end": **********.865352, "duration": 0.011359930038452148, "duration_str": "11.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.236138, "relative_start": 10.**************, "end": **********.237598, "relative_end": **********.237598, "duration": 0.0014598369598388672, "duration_str": "1.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 1047, "nb_visible_statements": 500, "nb_excluded_statements": 547, "nb_failed_statements": 0, "accumulated_duration": 0.8231099999999991, "accumulated_duration_str": "823ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.8791711, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.055}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.886969, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.055, "width_percent": 0.044}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.446825, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.098, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.449508, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.152, "width_percent": 0.095}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('VOLVO', '<EMAIL>', '', '', '', '', '', '', '', '', '', '', 'v.png', null, null, null, null, 1, 'pending', '2025-06-13 17:10:01', '2025-06-13 17:10:01')", "type": "query", "params": [], "bindings": ["VOLVO", "<EMAIL>", "", "", "", "", "", "", "", "", "", "", "v.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", "2025-06-13 17:10:01"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.456203, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 0.247, "width_percent": 0.48}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.464645, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.727, "width_percent": 0.067}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.466802, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.793, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = 'Komatsu', `logo` = 'k.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "k.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.476731, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 0.861, "width_percent": 0.482}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.4842331, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.344, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.4865048, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.403, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = 'HYUNDAI', `logo` = 'hy.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["HYUNDAI", "hy.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4957159, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 1.477, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.504317, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.929, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.506289, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.994, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = 'Caterpillar', `logo` = 'c.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Caterpillar", "c.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5156372, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 2.06, "width_percent": 0.463}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.523108, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.523, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.524969, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.585, "width_percent": 0.075}, {"sql": "update `mp_stores` set `name` = 'DOOSAN', `logo` = 'doosan-logo.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["DOOSAN", "doosan-logo.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.536061, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 2.661, "width_percent": 0.475}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.543293, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.136, "width_percent": 0.067}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.545219, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.202, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = '<PERSON>', `logo` = 'm.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Mercedes", "m.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5553608, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 3.277, "width_percent": 0.479}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.562618, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.755, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.564959, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.817, "width_percent": 0.106}, {"sql": "update `mp_stores` set `name` = 'Scania', `logo` = 's.png', `logo_square` = 'dhims.jpg', `content` = '<p>scania details</p>', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Scania", "s.png", "dhims.jpg", "<p>scania details</p>", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.576713, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 3.923, "width_percent": 0.487}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.586694, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.41, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.588705, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.483, "width_percent": 0.075}, {"sql": "update `mp_stores` set `name` = 'JCB', `logo` = 'jcb.png', `logo_square` = null, `content` = '', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["JCB", "jcb.png", null, "", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.603453, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 4.558, "width_percent": 0.518}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.611805, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.076, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6139798, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.14, "width_percent": 0.095}, {"sql": "update `mp_stores` set `name` = 'MAN', `logo` = 'man.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["MAN", "man.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.624679, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 5.235, "width_percent": 0.507}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6346362, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.742, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6372552, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.819, "width_percent": 0.118}, {"sql": "update `mp_stores` set `name` = 'TATA', `logo` = 'tata.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["TATA", "tata.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6489599, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 5.937, "width_percent": 0.516}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.661924, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.454, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.664325, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.524, "width_percent": 0.102}, {"sql": "update `mp_stores` set `name` = 'Daewoo', `logo` = 'daewoo.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "daewoo.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.675763, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 6.626, "width_percent": 0.493}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.684087, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.119, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6880188, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.18, "width_percent": 0.137}, {"sql": "update `ec_products` set `status` = 'published', `ec_products`.`updated_at` = '2025-06-13 17:10:01' where `ec_products`.`store_id` = 64 and `ec_products`.`store_id` is not null and `status` = 'blocked'", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64, "blocked"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 93}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 25, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 26, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.70712, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Store.php:93", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=93", "ajax": false, "filename": "Store.php", "line": "93"}, "connection": "muhrak", "explain": null, "start_percent": 7.317, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = 'HITACHI', `logo` = 'icons/hitachi-logo.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["HITACHI", "icons/hitachi-logo.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7149842, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 7.388, "width_percent": 0.524}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.724474, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.911, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.72752, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.971, "width_percent": 0.073}, {"sql": "update `mp_stores` set `name` = 'Siemens', `logo` = 'siemens.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Siemens", "siemens.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.740573, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 8.044, "width_percent": 0.454}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.748284, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.498, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.750756, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.561, "width_percent": 0.075}, {"sql": "update `mp_stores` set `name` = 'General Electric', `logo` = 'ge.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["General Electric", "ge.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.760757, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 8.637, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.771506, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.081, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.773624, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.142, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = 'Honeywell International', `logo` = 'hwl.png', `logo_square` = 'eriezmetaldetectorpharmaceutical.jpg', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Honeywell International", "hwl.png", "eriezmetaldetectorpharmaceutical.jpg", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.788826, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 9.204, "width_percent": 0.504}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.802053, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.708, "width_percent": 0.087}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.80498, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.796, "width_percent": 0.083}, {"sql": "update `mp_stores` set `name` = 'ABB', `logo` = 'abb.png', `logo_square` = null, `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["ABB", "abb.png", null, {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.819185, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 9.878, "width_percent": 0.451}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.831301, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.329, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8340008, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.385, "width_percent": 0.091}, {"sql": "update `mp_stores` set `name` = 'Mitsubishi Electric', `logo` = 'mitsubishi-electric.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Mitsubishi Electric", "mitsubishi-electric.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.852291, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 10.476, "width_percent": 0.49}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8633301, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.966, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.865997, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.023, "width_percent": 0.101}, {"sql": "update `mp_stores` set `name` = 'Schneider Electric', `logo` = 'schneider-electric.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Schneider Electric", "schneider-electric.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.885639, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 11.124, "width_percent": 0.519}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.893595, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.642, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.896648, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.698, "width_percent": 0.106}, {"sql": "update `mp_stores` set `name` = 'Emerson Electric Co', `logo` = 'emerson-electric-co.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Emerson Electric Co", "emerson-electric-co.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.913172, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 11.804, "width_percent": 0.504}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.923053, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.308, "width_percent": 0.111}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.926164, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.419, "width_percent": 0.157}, {"sql": "update `mp_stores` set `name` = 'LG Electronics', `logo` = 'lg.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["LG Electronics", "lg.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9508488, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 12.575, "width_percent": 0.565}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.963395, "duration": 0.00642, "duration_str": "6.42ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.14, "width_percent": 0.78}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.972867, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.92, "width_percent": 0.328}, {"sql": "update `mp_stores` set `name` = 'Samsung Electronics', `logo` = 'sam.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:01' where `id` = 64", "type": "query", "params": [], "bindings": ["Samsung Electronics", "sam.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:01", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.994466, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 14.248, "width_percent": 0.482}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.004703, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.731, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0082572, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.815, "width_percent": 0.103}, {"sql": "update `mp_stores` set `name` = 'Bosch Group', `logo` = 'bosch-group.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["Bosch Group", "bosch-group.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.029872, "duration": 0.0064, "duration_str": "6.4ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 14.918, "width_percent": 0.778}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0449388, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.695, "width_percent": 0.074}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.048568, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.769, "width_percent": 0.147}, {"sql": "update `mp_stores` set `name` = 'RS Pro', `logo` = 'icons/rs.webp', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["RS Pro", "icons/rs.webp", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.071727, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 15.916, "width_percent": 0.525}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0846488, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.441, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0877519, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.513, "width_percent": 0.113}, {"sql": "update `mp_stores` set `name` = 'MKS HYDRAULIC', `logo` = 'logo.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["MKS HYDRAULIC", "logo.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.111297, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 16.626, "width_percent": 0.453}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.120165, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.079, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.122906, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.141, "width_percent": 0.079}, {"sql": "update `mp_stores` set `name` = 'Guangzhou Jiajue Machinery Equipment', `logo` = '305480112-518175496979076-6001249714432641489-n.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["Guangzhou Jiajue Machinery Equipment", "305480112-518175496979076-6001249714432641489-n.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.142057, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 17.22, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.150835, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.665, "width_percent": 0.1}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.153457, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.764, "width_percent": 0.081}, {"sql": "update `mp_stores` set `name` = 'ComeSys', `logo` = 'selection-113.png', `content` = '<p>Since its establishment in 1990, ComSys has provided solutions for Mobile and Industrial Applications. These include forklifts, construction equipment, agricultural machinery, buses, golf carts, Harbor tractors, ships, and special vehicles. </p><p> </p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["ComeSys", "selection-113.png", "<p>Since its establishment in 1990, ComSys has provided solutions for Mobile and Industrial Applications. These include forklifts, construction equipment, agricultural machinery, buses, golf carts, Harbor tractors, ships, and special vehicles. </p><p> </p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.165329, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 17.846, "width_percent": 0.477}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.1741042, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.323, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.177198, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.378, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = 'I-Tork', `logo` = 'i-trok-accutators-uae.png', `content` = '<p><strong>I-Tork</strong> is a company specializing in the design and manufacture of industrial valve automation products, including electric and pneumatic actuators, limit switches, and various accessories for valve control. The company is known for its high-quality products, which are used in various industries, such as oil and gas, water treatment, petrochemical, power generation.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["I-Tork", "i-trok-accutators-uae.png", "<p><strong>I-Tork</strong> is a company specializing in the design and manufacture of industrial valve automation products, including electric and pneumatic actuators, limit switches, and various accessories for valve control. The company is known for its high-quality products, which are used in various industries, such as oil and gas, water treatment, petrochemical, power generation.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1963758, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 18.448, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.2060802, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.904, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.208076, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.957, "width_percent": 0.078}, {"sql": "update `mp_stores` set `name` = 'Liming', `logo` = 'liming-logo2.png', `logo_square` = 'aboutlm71500x700.jpg', `description` = 'LI MING MACHINERY CO., LTD. is specialist in design, manufacturing of wide range of high-tech speed reduction motor and helical gear reducers, worm gear reducers and planetary gear reducers. In recent', `content` = '<p>Li-Ming Machinery Co., Ltd. (also known as Liming Machinery) is a well-established company specializing in the production of precision gear reducers and power transmission equipment.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["Liming", "liming-logo2.png", "aboutlm71500x700.jpg", "LI MING MACHINERY CO., LTD. is specialist in design, manufacturing of wide range of high-tech speed reduction motor and helical gear reducers, worm gear reducers and planetary gear reducers. In recent", "<p>Li-Ming Machinery Co., Ltd. (also known as Liming Machinery) is a well-established company specializing in the production of precision gear reducers and power transmission equipment.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.222593, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 19.035, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.232385, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 19.494, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.234809, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 19.547, "width_percent": 0.095}, {"sql": "update `mp_stores` set `name` = '<PERSON>rlekar Precision', `logo` = 'hirlekar-1.png', `logo_square` = 'hkk.png', `description` = 'Since 1974 Hirlekar Precision was started in the year 1974 by the Late <PERSON><PERSON> as a manufacturer of world class gauge sector mechanisms used in the instrumentation industry. Starting off in', `content` = '', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> Precision", "hirlekar-1.png", "hkk.png", "Since 1974 <PERSON><PERSON>kar Precision was started in the year 1974 by the Late <PERSON><PERSON> as a manufacturer of world class gauge sector mechanisms used in the instrumentation industry. Starting off in", "", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.250695, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 19.641, "width_percent": 0.524}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.268258, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.165, "width_percent": 0.086}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.270624, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.251, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = 'KTNF', `logo` = 'ci.png', `logo_square` = 'innovative-it-infrastructure-solution-provider-with-global-competitiveness.png?v=**********', `description` = 'Innovative IT Infrastructure Solution Provider with Global Competitiveness', `content` = '<p>Innovative IT Infrastructure Solution Provider with Global Competitiveness</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["KTNF", "ci.png", "innovative-it-infrastructure-solution-provider-with-global-competitiveness.png?v=**********", "Innovative IT Infrastructure Solution Provider with Global Competitiveness", "<p>Innovative IT Infrastructure Solution Provider with Global Competitiveness</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.284625, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 20.32, "width_percent": 0.468}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.303035, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.788, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.304981, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.842, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = 'KP Electric Co., Ltd', `logo` = 'kpp.png', `logo_square` = 'kp-electric-co-ltd.jpg?v=**********', `description` = 'provider of electrical and power solutions, design, manufacturing', `content` = '<p>provider of electrical and power solutions, design, manufacturing</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["KP Electric Co., Ltd", "kpp.png", "kp-electric-co-ltd.jpg?v=**********", "provider of electrical and power solutions, design, manufacturing", "<p>provider of electrical and power solutions, design, manufacturing</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.321193, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 20.907, "width_percent": 0.468}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.3390138, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.375, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.340887, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.424, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = 'KT&amp;C', `logo` = 'ktc.png', `logo_square` = 'thumb-16580-1.png?v=1730924790', `description` = 'Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras an', `content` = '<p><strong>KT&amp;C</strong> (Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras and video systems.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:10:02' where `id` = 64", "type": "query", "params": [], "bindings": ["KT&amp;C", "ktc.png", "thumb-16580-1.png?v=1730924790", "Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras an", "<p><strong>KT&amp;C</strong> (Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras and video systems.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:10:02", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.358463, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 21.478, "width_percent": 0.419}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.3677359, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.897, "width_percent": 0.087}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.370089, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.985, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390276, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.047, "width_percent": 0.436}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3986878, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.483, "width_percent": 0.095}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400497, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.578, "width_percent": 0.086}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.41407, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.664, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4224339, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.12, "width_percent": 0.068}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423552, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.188, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4369879, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.258, "width_percent": 0.497}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4562201, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.755, "width_percent": 0.079}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458122, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.834, "width_percent": 0.09}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.47822, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.924, "width_percent": 0.457}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4943671, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.381, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4954162, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.441, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.513473, "duration": 0.00936, "duration_str": "9.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.499, "width_percent": 1.137}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.527344, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.636, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.528359, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.693, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.545825, "duration": 0.00744, "duration_str": "7.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.756, "width_percent": 0.904}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.558208, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.66, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.559254, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.722, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.571943, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.787, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5806391, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.26, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.581693, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.32, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5951009, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.38, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604172, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.832, "width_percent": 0.068}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.605311, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.9, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.619754, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.968, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6283321, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.429, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629361, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.49, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6462321, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.556, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654817, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.017, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655845, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.075, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.669169, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.147, "width_percent": 0.45}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677541, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.596, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67858, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.656, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693886, "duration": 0.013550000000000001, "duration_str": "13.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.718, "width_percent": 1.646}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712797, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.364, "width_percent": 0.068}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7140188, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.432, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726733, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.506, "width_percent": 0.419}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7352579, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.925, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736548, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.997, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748579, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.059, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.756639, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.514, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757583, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.567, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770559, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.63, "width_percent": 0.463}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.779145, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.093, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780064, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.135, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7922099, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.192, "width_percent": 0.476}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8015518, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.669, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.802721, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.74, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8149362, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.802, "width_percent": 0.507}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8258781, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.309, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.826879, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.367, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839328, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.439, "width_percent": 0.463}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.847748, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.902, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.848877, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.961, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.861719, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.029, "width_percent": 0.477}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.874196, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.507, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.875243, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.568, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889458, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.63, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898233, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.085, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.899786, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.148, "width_percent": 0.079}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91237, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.227, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9222052, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.683, "width_percent": 0.075}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923755, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.758, "width_percent": 0.083}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946883, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.841, "width_percent": 0.451}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955393, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.291, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956482, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.356, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.972463, "duration": 0.00937, "duration_str": "9.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.423, "width_percent": 1.138}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99082, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.561, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9919379, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.622, "width_percent": 0.073}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.004828, "duration": 0.0175, "duration_str": "17.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.695, "width_percent": 2.126}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.0269449, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.821, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.0278661, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.865, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.044014, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.922, "width_percent": 0.492}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.053986, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.414, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.0550668, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.478, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.0650609, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.552, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.072445, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.025, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.0733361, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.071, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.084364, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.133, "width_percent": 0.447}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.0935872, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.58, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.094628, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.638, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.1104121, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.701, "width_percent": 0.425}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.122085, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.127, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.123102, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.185, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.134569, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.253, "width_percent": 0.453}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.1418781, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.706, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.14288, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.766, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.157052, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.831, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.163977, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.249, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.165188, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.297, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.177818, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.363, "width_percent": 0.43}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.185928, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.794, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.1869211, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.847, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.19754, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.907, "width_percent": 0.467}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.205576, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.373, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.206781, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.444, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.21992, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.513, "width_percent": 0.436}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.227303, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.949, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.228286, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.999, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.243125, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.053, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.2512481, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.487, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.252441, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.541, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.2654061, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.615, "width_percent": 0.467}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.274588, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.081, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.275795, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.143, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.292765, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.209, "width_percent": 0.454}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.302534, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.663, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.303597, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.723, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3135629, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.785, "width_percent": 0.492}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.321732, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.277, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3228319, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.337, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3335311, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.403, "width_percent": 0.488}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3410869, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.891, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.34199, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.938, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.353847, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.996, "width_percent": 0.463}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3613, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.459, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3623009, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.509, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3735409, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.574, "width_percent": 0.448}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.380614, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.022, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.381659, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.076, "width_percent": 0.083}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.3925982, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.159, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.400362, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.576, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.401771, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.654, "width_percent": 0.081}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.41185, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.736, "width_percent": 0.423}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.419835, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.158, "width_percent": 0.085}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.421094, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.243, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.431387, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.304, "width_percent": 0.433}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.438691, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.737, "width_percent": 0.067}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.439814, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.804, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.452717, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.867, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.4637961, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.328, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.464811, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.382, "width_percent": 0.081}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.4761531, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.463, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.484622, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.922, "width_percent": 0.083}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.486052, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.005, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.497679, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.073, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.5052261, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.535, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.506233, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.594, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.518126, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.655, "width_percent": 0.481}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.5266008, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.136, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.5275462, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.188, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.5404718, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.247, "width_percent": 0.436}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.548738, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.683, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.549745, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.728, "width_percent": 0.085}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.5631652, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.813, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.571576, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.272, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.572623, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.33, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.5854208, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.39, "width_percent": 0.435}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.593315, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.825, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.594398, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.889, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.607123, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.955, "width_percent": 0.425}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.6149979, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.38, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.6159968, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.429, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.628605, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.503, "width_percent": 0.43}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.637027, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.933, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.638114, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.989, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.650022, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.06, "width_percent": 0.398}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.657608, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.459, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.658613, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.518, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.675182, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.588, "width_percent": 0.49}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.6840122, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.077, "width_percent": 0.075}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.6853719, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.153, "width_percent": 0.09}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.697485, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.243, "width_percent": 0.485}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.7064571, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.727, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.707427, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.783, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.719783, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.846, "width_percent": 0.47}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.7281458, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.316, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.729085, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.369, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.7414021, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.431, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.749842, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.891, "width_percent": 0.067}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.751245, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.958, "width_percent": 0.097}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.763007, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.055, "width_percent": 0.458}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.776134, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.513, "width_percent": 0.069}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.777225, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.582, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.789947, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.652, "width_percent": 0.377}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.797634, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.028, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.798563, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.074, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.810574, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.138, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.823481, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.598, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.824597, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.66, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.836648, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.728, "width_percent": 0.426}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.84437, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.155, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.845317, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.206, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.85749, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.275, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.866873, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.712, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.868264, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.777, "width_percent": 0.106}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.8827958, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.882, "width_percent": 0.454}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.890805, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.337, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.891746, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.389, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.903821, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.453, "width_percent": 0.469}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.9125478, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.922, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.913475, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.972, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.926143, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.032, "width_percent": 0.384}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.934846, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.415, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.936027, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.486, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.94703, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.548, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.955302, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.007, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.9563289, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.064, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.9687512, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.13, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.9768848, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.591, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.977894, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.641, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834603.993946, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.703, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.0052521, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.162, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.006299, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.22, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.018674, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.288, "width_percent": 0.491}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.027095, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.778, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.028029, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.832, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.042012, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.894, "width_percent": 0.347}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.049943, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.241, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.051524, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.3, "width_percent": 0.111}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.0639598, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.41, "width_percent": 0.453}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.0726562, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.863, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.0737221, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.924, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.086239, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.985, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.094143, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.422, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.095088, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.474, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.1074011, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.54, "width_percent": 0.356}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.115066, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.896, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.1162682, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.959, "width_percent": 0.075}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.14059, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.035, "width_percent": 0.391}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.1484392, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.426, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.149474, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.482, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.161669, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.544, "width_percent": 0.468}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.172218, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.011, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.1732829, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.072, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.185288, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.14, "width_percent": 0.48}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.193851, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.62, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.194839, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.676, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.212682, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.739, "width_percent": 0.351}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.220392, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.09, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.221516, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.15, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.234229, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.216, "width_percent": 0.405}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.241935, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.621, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.242939, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.681, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.2598221, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.741, "width_percent": 0.38}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.268473, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.122, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.269695, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.192, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.283401, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.255, "width_percent": 0.264}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.290374, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.519, "width_percent": 0.066}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.291517, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.584, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.307549, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.654, "width_percent": 0.401}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.3152611, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.055, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.3162699, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.113, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.329165, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.183, "width_percent": 0.446}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.342098, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.629, "width_percent": 0.068}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.343208, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.697, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.35757, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.76, "width_percent": 0.385}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.365211, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.146, "width_percent": 0.067}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.3663008, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.212, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.379663, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.284, "width_percent": 0.25}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.390328, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.534, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.3913589, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.596, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.407864, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.666, "width_percent": 0.255}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.414324, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.921, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.415367, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.975, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.427719, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.04, "width_percent": 0.248}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.435526, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.288, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.436694, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.359, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.447878, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.422, "width_percent": 0.214}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.458566, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.636, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.459575, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.696, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.472252, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.759, "width_percent": 0.242}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.478718, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.001, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.479686, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.054, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.492183, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.12, "width_percent": 0.255}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.498883, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.375, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.499934, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.432, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.5124948, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.5, "width_percent": 0.242}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.519633, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.742, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.520746, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.805, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.5318751, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.876, "width_percent": 0.203}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.538697, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.078, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.5397768, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.143, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.5539532, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.217, "width_percent": 0.248}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.560426, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.465, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.561326, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.512, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.574807, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.578, "width_percent": 0.237}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.581097, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.815, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.5821111, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.866, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.595068, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.929, "width_percent": 0.243}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.603663, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.172, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.604718, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.231, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.6159549, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.295, "width_percent": 0.221}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.6225631, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.516, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.623632, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.576, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.636806, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.638, "width_percent": 0.213}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.642937, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.851, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.6439438, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.91, "width_percent": 0.073}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.6570349, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.983, "width_percent": 0.262}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.66375, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.246, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.664733, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.297, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.6767519, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.359, "width_percent": 0.259}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.6833851, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.618, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.684493, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.676, "width_percent": 0.084}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.697056, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.76, "width_percent": 0.259}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.707889, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.018, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.708981, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.082, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.725609, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.146, "width_percent": 0.205}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.73176, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.351, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.732669, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.395, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.746247, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.452, "width_percent": 0.216}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.753299, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.668, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.754332, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.726, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.7672, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.789, "width_percent": 0.3}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.774147, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.089, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.7752, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.145, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.787847, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.212, "width_percent": 0.271}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.794433, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.482, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.795458, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.537, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.808183, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.6, "width_percent": 0.25}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.814611, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.851, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.815645, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.908, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.8317542, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.971, "width_percent": 0.225}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.83851, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.196, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.839578, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.259, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.851538, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.328, "width_percent": 0.261}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.862248, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.589, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.863246, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.649, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.8757992, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.713, "width_percent": 0.249}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.8824332, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.962, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.88354, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.018, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.899662, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.087, "width_percent": 0.259}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.910263, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.346, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.911315, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.406, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.923826, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.471, "width_percent": 0.233}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.9302292, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.704, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.931185, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.758, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.951904, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.826, "width_percent": 0.249}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.9583921, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.075, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.9594579, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.132, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.9717882, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.198, "width_percent": 0.249}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.978251, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.447, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.979173, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.497, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834604.993444, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.56, "width_percent": 0.261}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.0000021, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.821, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.001211, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.876, "width_percent": 0.084}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.014497, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.959, "width_percent": 0.238}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.026369, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.198, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.027463, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.257, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.03959, "duration": 0.00738, "duration_str": "7.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.326, "width_percent": 0.897}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.052699, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.223, "width_percent": 0.081}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.053953, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.304, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.067135, "duration": 0.00736, "duration_str": "7.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.368, "width_percent": 0.894}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.078949, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.262, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.079932, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.314, "width_percent": 0.063}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.0930278, "duration": 0.00725, "duration_str": "7.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.377, "width_percent": 0.881}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.106373, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.258, "width_percent": 0.063}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.107486, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.321, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.1197472, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.382, "width_percent": 0.888}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.131104, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.27, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.132028, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.32, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.14694, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.381, "width_percent": 0.888}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.15926, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.269, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.1602569, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.326, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.172767, "duration": 0.00704, "duration_str": "7.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.395, "width_percent": 0.855}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.184684, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.25, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.185917, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.307, "width_percent": 0.086}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.197874, "duration": 0.0071200000000000005, "duration_str": "7.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.394, "width_percent": 0.865}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.2094982, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.259, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.210556, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.317, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.229532, "duration": 0.00736, "duration_str": "7.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.376, "width_percent": 0.894}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.2417672, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.271, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.242796, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.33, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.254184, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.397, "width_percent": 0.381}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.260918, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.778, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.261864, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.828, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.278831, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.888, "width_percent": 0.392}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.288151, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.28, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.289193, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.339, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.300514, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.401, "width_percent": 0.383}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.308062, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.783, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.309114, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.842, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.32167, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.903, "width_percent": 0.386}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.3292909, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.29, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.3301702, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.335, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.342184, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.392, "width_percent": 0.357}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.349977, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.749, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.351054, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.796, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.375515, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.868, "width_percent": 0.45}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.383965, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.318, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.3851361, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.369, "width_percent": 0.1}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.397333, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.468, "width_percent": 0.453}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.406487, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.921, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.4076538, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.986, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.4211419, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.056, "width_percent": 0.453}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.429553, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.509, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.430459, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.554, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.444878, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.61, "width_percent": 0.446}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.453663, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.056, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.454775, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.117, "width_percent": 0.077}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.468302, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.193, "width_percent": 0.492}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.477471, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.685, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.4785001, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.743, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.491369, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.803, "width_percent": 0.468}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.50023, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.271, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.5013459, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.326, "width_percent": 0.087}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.5143511, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.413, "width_percent": 0.463}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.525321, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.876, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834605.526413, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.94, "width_percent": 0.06}, {"sql": "... 547 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Customer": {"value": 348, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 347, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 698, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/import/import", "action_name": "marketplace.store.import.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import", "uri": "POST admin/marketplaces/stores/import/import", "permission": "marketplace.store.import", "controller": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores/import", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:70-118</a>", "middleware": "web, core, auth", "duration": "10.2s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2112857794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2112857794\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1311727951 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Stores-example-27de822beeecb562fa062d3c57a45a01-684c5b68044c8.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>total</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311727951\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2044628209 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZDWmR4VG12MG0xY0hqYnJvbE9oZWc9PSIsInZhbHVlIjoiVDRjZlhydjdxQ2IzazVQSndFRU5yMnB5cksrdVRVR0NLQzRwcE5WR2dqRWw1b2UzNmwvam4zYkxBZFFGRW5nTTN2SG5lUzduVTFMZmhyYzl4N2RWMW9WaktvTWVnUi9iaUs5cW0vVkpVVkRjNTV3QjMyUTRhTVpXd1hTbTN4RzMiLCJtYWMiOiI5NTY2MGQ3OWZiNjI3YWZiMmM4Y2ExZjU4NGM2MDE1MmNkYTY0ZWMyZGVjMWQ3NmIwNzU3NjY3ODFiZTMwZWU3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">722</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryQ9ptGoPvnhMuVudA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">https://muhrak.gc/admin/marketplaces/stores/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZDWmR4VG12MG0xY0hqYnJvbE9oZWc9PSIsInZhbHVlIjoiVDRjZlhydjdxQ2IzazVQSndFRU5yMnB5cksrdVRVR0NLQzRwcE5WR2dqRWw1b2UzNmwvam4zYkxBZFFGRW5nTTN2SG5lUzduVTFMZmhyYzl4N2RWMW9WaktvTWVnUi9iaUs5cW0vVkpVVkRjNTV3QjMyUTRhTVpXd1hTbTN4RzMiLCJtYWMiOiI5NTY2MGQ3OWZiNjI3YWZiMmM4Y2ExZjU4NGM2MDE1MmNkYTY0ZWMyZGVjMWQ3NmIwNzU3NjY3ODFiZTMwZWU3IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InJZakd5N1oxSTF0SDJCcHd4ZGVXNlE9PSIsInZhbHVlIjoidzlySG5YemFLc1R4a2VMUVZPd29vcVZYL0QzQUlTRk1DdWcxRWZwa0dtSVpTSE9IdElyMzRwZUNJTVpwWVBhNkkrVHFBdFZ2cFdVd2hPU05Vc2V0UDZESGRrcWphWm44NHhhckVpNVgxNUxRaXFzL2pHNnI2VUE3L2lYUWdCMDAiLCJtYWMiOiIzZjE1MjU2YWZhOTIxNGViZDdkOWU3NzQyYzBjMzc5MjFjNTVjN2E0MGI4ZmU3ZjczOTAxNGVkNzEyMjliN2Y0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044628209\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-597164461 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:10:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597164461\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1341866246 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">https://muhrak.gc/admin/marketplaces/stores/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341866246\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/import/import", "action_name": "marketplace.store.import.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import"}, "badge": null}}