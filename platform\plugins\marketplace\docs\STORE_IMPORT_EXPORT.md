# Store Import/Export Documentation

This document describes the Store Import/Export functionality for the Marketplace plugin.

## Overview

The Store Import/Export feature allows administrators and vendors to:
- Import stores from CSV/Excel files
- Export stores to CSV/Excel files
- Bulk manage store data
- Maintain data consistency and validation

## Features

### Import Features
- **CSV/Excel Support**: Import stores from CSV or Excel files
- **Data Validation**: Comprehensive validation for all store fields
- **Customer Association**: Automatic customer linking via ID or email
- **Error Handling**: Detailed error reporting for failed imports
- **Update Existing**: Support for updating existing stores
- **Batch Processing**: Efficient handling of large datasets

### Export Features
- **Flexible Export**: Export all stores or vendor-specific stores
- **Column Selection**: Choose which columns to export
- **Multiple Formats**: Support for CSV and Excel formats
- **Real-time Data**: Always exports current store data
- **Customer Information**: Includes associated customer details

## Usage

### Admin Panel

#### Importing Stores (Method 1: Direct Access)
1. Navigate to **Marketplace > Stores**
2. Click the **Import** button in the header
3. Download the example file to see the required format
4. Upload your CSV/Excel file
5. Review validation results
6. Confirm the import

#### Importing Stores (Method 2: Tools Menu)
1. Navigate to **Tools > Data Synchronize**
2. Find **Stores** in the Import section
3. Click on the Stores import option
4. Follow the same import process

#### Exporting Stores (Method 1: Direct Access)
1. Navigate to **Marketplace > Stores**
2. Click the **Export** button in the header
3. Select desired columns
4. Choose export format (CSV/Excel)
5. Download the generated file

#### Exporting Stores (Method 2: Tools Menu)
1. Navigate to **Tools > Data Synchronize**
2. Find **Stores** in the Export section
3. Click on the Stores export option
4. Follow the same export process

### Vendor Dashboard

#### Importing Stores (Vendor)
1. Navigate to vendor dashboard
2. Go to **Import > Stores**
3. Follow the same process as admin import
4. Note: Vendors can only import stores for their own account

#### Exporting Stores (Vendor)
1. Navigate to vendor dashboard
2. Go to **Export > Stores**
3. Export only shows the vendor's own store
4. Follow the same process as admin export

## File Format

### Required Columns
- `name`: Store name (required)
- `email`: Store email (required)
- `customer_email` or `customer_id`: Customer association (required)

### Optional Columns
- `phone`: Store phone number
- `address`: Store address
- `country`: Store country
- `state`: Store state/province
- `city`: Store city
- `zip_code`: Store postal code
- `company`: Company name
- `tax_id`: Tax identification number
- `description`: Store description
- `content`: Store detailed content
- `logo`: Store logo image path
- `logo_square`: Store square logo image path
- `cover_image`: Store cover image path
- `certificate_file`: Business certificate file path
- `government_id_file`: Government ID file path
- `status`: Store status (published, pending, etc.)

### Example CSV Format
```csv
name,email,phone,address,country,state,city,zip_code,company,tax_id,description,content,logo,logo_square,cover_image,certificate_file,government_id_file,status,customer_email
"Example Store","<EMAIL>","+1234567890","123 Main St","United States","California","Los Angeles","90210","Example LLC","TAX123","Store description","Detailed content","stores/logo.png","stores/logo-square.png","stores/cover.jpg","stores/certificate.pdf","stores/government-id.pdf","published","<EMAIL>"
```

## Validation Rules

### Store Fields
- **name**: Required, string, max 255 characters
- **email**: Required, valid email format, max 255 characters
- **phone**: Optional, string, max 20 characters
- **address**: Optional, string, max 500 characters
- **country**: Optional, string, max 120 characters
- **state**: Optional, string, max 120 characters
- **city**: Optional, string, max 120 characters
- **zip_code**: Optional, string, max 20 characters
- **company**: Optional, string, max 255 characters
- **tax_id**: Optional, string, max 255 characters
- **description**: Optional, string, max 400 characters
- **content**: Optional, string (no limit)
- **logo**: Optional, string, max 255 characters (file path)
- **logo_square**: Optional, string, max 255 characters (file path)
- **cover_image**: Optional, string, max 255 characters (file path)
- **certificate_file**: Optional, string, max 255 characters (file path)
- **government_id_file**: Optional, string, max 255 characters (file path)
- **status**: Optional, must be valid store status
- **customer_id**: Optional, must exist in customers table
- **customer_email**: Optional, must exist in customers table

## Error Handling

### Common Import Errors
1. **Customer not found**: The specified customer_id or customer_email doesn't exist
2. **Invalid email format**: The store email is not in valid format
3. **Missing required fields**: Name or email is missing
4. **Invalid status**: Status value is not recognized
5. **Duplicate email**: Store email already exists (when creating new stores)

### Error Resolution
- Review the error report after import validation
- Fix data issues in your source file
- Re-upload the corrected file
- Contact support for persistent issues

## Permissions

### Admin Permissions
- `marketplace.store.import`: Required for store import
- `marketplace.store.export`: Required for store export
- `marketplace.store.index`: Required to access store management

### Vendor Permissions
- Vendors can only import/export their own stores
- No additional permissions required beyond vendor access

## Technical Details

### Files Created
- `StoreImporter.php`: Handles store import logic
- `StoreExporter.php`: Handles store export logic
- `ImportStoreController.php`: Admin import controller
- `ExportStoreController.php`: Admin export controller
- `ImportStoreController.php` (Fronts): Vendor import controller
- `ExportStoreController.php` (Fronts): Vendor export controller

### Routes Added
- Admin routes: `/admin/marketplace/stores/import/*` and `/admin/marketplace/stores/export/*`
- Vendor routes: `/vendor/import/stores/*` and `/vendor/export/stores/*`

### Database Impact
- No new tables required
- Uses existing `mp_stores` and `ec_customers` tables
- Maintains referential integrity

## Best Practices

1. **Backup Data**: Always backup your database before large imports
2. **Test Small Batches**: Test with small files before importing large datasets
3. **Validate Data**: Use the validation feature before final import
4. **Customer Preparation**: Ensure all customers exist before importing stores
5. **Regular Exports**: Regularly export store data for backup purposes

## Troubleshooting

### Import Issues
- Verify file format matches the example
- Check customer emails exist in the system
- Ensure required fields are not empty
- Validate file encoding (UTF-8 recommended)

### Export Issues
- Check permissions for export functionality
- Verify sufficient disk space for large exports
- Ensure browser allows file downloads

## Support

For additional support or questions about the Store Import/Export functionality:
1. Check the error messages for specific guidance
2. Review this documentation
3. Contact your system administrator
4. Submit a support ticket if issues persist
