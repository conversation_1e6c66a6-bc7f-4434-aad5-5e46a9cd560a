<?php

namespace Database\Seeders;

use <PERSON><PERSON><PERSON>\ACL\Database\Seeders\UserSeeder;
use Botble\Base\Supports\BaseSeeder;
use Bo<PERSON>ble\Ecommerce\Database\Seeders\CurrencySeeder;
use Bo<PERSON>ble\Ecommerce\Database\Seeders\ProductSpecificationSeeder;
use Bo<PERSON>ble\Ecommerce\Database\Seeders\ReviewSeeder;
use Botble\Ecommerce\Database\Seeders\ShippingSeeder;
use Botble\Ecommerce\Database\Seeders\TaxSeeder;
use Botble\Language\Database\Seeders\LanguageSeeder;

class DatabaseSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->prepareRun();

        $this->call([
            // LanguageSeeder::class,
            // BrandSeeder::class,
            // CurrencySeeder::class,
            // ProductCategorySeeder::class,
            // ProductCollectionSeeder::class,
            // ProductLabelSeeder::class,
            // ProductSeeder::class,
            // ProductAttributeSeeder::class,
            // CustomerSeeder::class,
            // ReviewSeeder::class,
            // TaxSeeder::class,
            // ProductTagSeeder::class,
            // FlashSaleSeeder::class,
            // ShippingSeeder::class,
            // ContactSeeder::class,
            // UserSeeder::class,
            // BlogSeeder::class,
            // SimpleSliderSeeder::class,
            // PageSeeder::class,
            // AdsSeeder::class,
            // FaqSeeder::class,
            // SettingSeeder::class,
            // ProductSpecificationSeeder::class,
            // StoreLocatorSeeder::class,
            // MenuSeeder::class,
            // ThemeOptionSeeder::class,
            // WidgetSeeder::class,
            // ProductOptionSeeder::class,
            // MarketplaceSeeder::class,
            // DistributorSeeder::class,
        ]);

        $this->finished();
    }
}
