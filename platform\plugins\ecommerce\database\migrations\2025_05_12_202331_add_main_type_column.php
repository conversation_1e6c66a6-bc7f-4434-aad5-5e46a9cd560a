<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('ec_products', function (Blueprint $table): void {
            $table->string('main_type')->nullable();
        });
        Schema::table('ec_product_categories', function (Blueprint $table): void {
            $table->string('main_type')->nullable();
        });
        Schema::table('ec_brands', function (Blueprint $table): void {
            $table->string('main_type')->nullable();
        });
        Schema::table('mp_distributors', function (Blueprint $table): void {
            $table->string('main_type')->nullable();
        });
        Schema::table('mp_stores', function (Blueprint $table): void {
            $table->string('main_type')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('ec_products', function (Blueprint $table): void {
            $table->dropColumn('main_type');
        });
        Schema::table('ec_product_categories', function (Blueprint $table): void {
            $table->dropColumn('main_type');
        });
        Schema::table('ec_brands', function (Blueprint $table): void {
            $table->dropColumn('main_type');
        });
        Schema::table('mp_distributors', function (Blueprint $table): void {
            $table->dropColumn('main_type');
        });
        Schema::table('mp_stores', function (Blueprint $table): void {
            $table->dropColumn('main_type');
        });
    }
};
