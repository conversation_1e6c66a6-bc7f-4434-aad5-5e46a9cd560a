<?php

namespace Bo<PERSON>ble\Marketplace\Http\Controllers\Tools;

use Bo<PERSON>ble\DataSynchronize\Http\Controllers\ImportController;
use Botble\DataSynchronize\Importer\Importer;
use Botble\Marketplace\Importers\ToolsStoreImporter;
use Illuminate\Http\Request;

class ImportStoreController extends ImportController
{
    protected function getImporter(): Importer
    {
        return ToolsStoreImporter::make();
    }

    protected function prepareImporter(Request $request): Importer
    {
        return parent::prepareImporter($request);
    }
}
