<?php

namespace Botble\Marketplace\Tests\Feature;

use Botble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Botble\Marketplace\Enums\StoreStatusEnum;
use Botble\Marketplace\Exporters\StoreExporter;
use Bo<PERSON>ble\Marketplace\Importers\StoreImporter;
use Botble\Marketplace\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class StoreImportExportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Activate the marketplace plugin
        BaseHelper::activatePlugin('marketplace');
        BaseHelper::activatePlugin('ecommerce');
    }

    public function test_store_exporter_can_export_stores(): void
    {
        // Create a customer
        $customer = Customer::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test Customer',
        ]);

        // Create a store
        $store = Store::factory()->create([
            'name' => 'Test Store',
            'email' => '<EMAIL>',
            'customer_id' => $customer->id,
            'status' => StoreStatusEnum::PUBLISHED,
        ]);

        $exporter = new StoreExporter();

        $this->assertTrue($exporter->hasDataToExport());

        $collection = $exporter->collection();

        $this->assertCount(1, $collection);
        $this->assertEquals('Test Store', $collection->first()['name']);
        $this->assertEquals('<EMAIL>', $collection->first()['email']);
        $this->assertEquals('<EMAIL>', $collection->first()['customer_email']);
    }

    public function test_store_importer_can_import_stores(): void
    {
        // Create a customer
        $customer = Customer::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test Customer',
        ]);

        $importer = new StoreImporter();

        $data = [
            [
                'name' => 'Imported Store',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'address' => '123 Main St',
                'country' => 'United States',
                'state' => 'California',
                'city' => 'Los Angeles',
                'zip_code' => '90210',
                'company' => 'Test Company',
                'tax_id' => 'TAX123',
                'description' => 'Test description',
                'content' => 'Test content',
                'logo' => 'stores/test-logo.png',
                'logo_square' => 'stores/test-logo-square.png',
                'cover_image' => 'stores/test-cover.jpg',
                'certificate_file' => 'stores/test-certificate.pdf',
                'government_id_file' => 'stores/test-government-id.pdf',
                'status' => StoreStatusEnum::PUBLISHED,
                'customer_email' => '<EMAIL>',
            ],
        ];

        $imported = $importer->handle($data);

        $this->assertEquals(1, $imported);

        $store = Store::where('email', '<EMAIL>')->first();

        $this->assertNotNull($store);
        $this->assertEquals('Imported Store', $store->name);
        $this->assertEquals('<EMAIL>', $store->email);
        $this->assertEquals($customer->id, $store->customer_id);
        $this->assertEquals(StoreStatusEnum::PUBLISHED, $store->status);
    }

    public function test_store_importer_handles_missing_customer(): void
    {
        $importer = new StoreImporter();

        $data = [
            [
                'name' => 'Test Store',
                'email' => '<EMAIL>',
                'customer_email' => '<EMAIL>',
            ],
        ];

        $imported = $importer->handle($data);

        $this->assertEquals(0, $imported);
        $this->assertCount(1, $importer->failures());
    }

    public function test_store_importer_validates_required_fields(): void
    {
        $importer = new StoreImporter();

        $columns = $importer->columns();

        // Check that required fields are properly configured
        $nameColumn = collect($columns)->firstWhere('name', 'name');
        $emailColumn = collect($columns)->firstWhere('name', 'email');

        $this->assertNotNull($nameColumn);
        $this->assertNotNull($emailColumn);
        $this->assertContains('required', $nameColumn->getRules());
        $this->assertContains('required', $emailColumn->getRules());
    }

    public function test_store_exporter_columns_configuration(): void
    {
        $exporter = new StoreExporter();

        $columns = $exporter->columns();

        $this->assertNotEmpty($columns);

        // Check that essential columns are present
        $columnNames = collect($columns)->pluck('name')->toArray();

        $this->assertContains('id', $columnNames);
        $this->assertContains('name', $columnNames);
        $this->assertContains('email', $columnNames);
        $this->assertContains('logo', $columnNames);
        $this->assertContains('logo_square', $columnNames);
        $this->assertContains('cover_image', $columnNames);
        $this->assertContains('certificate_file', $columnNames);
        $this->assertContains('government_id_file', $columnNames);
        $this->assertContains('customer_id', $columnNames);
        $this->assertContains('customer_email', $columnNames);
        $this->assertContains('status', $columnNames);
    }
}
