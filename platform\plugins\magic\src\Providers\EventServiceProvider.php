<?php
namespace <PERSON><PERSON>qi\BotbleActivator\Providers;

use Illuminate\Routing\Events\RouteMatched;
use Bo<PERSON>ble\PluginManagement\Events\ActivatedPluginEvent;
use Bo<PERSON>ble\Base\Http\Middleware\EnsureLicenseHasBeenActivated;
use <PERSON><PERSON>qi\BotbleActivator\Listeners\SkipLicenseReminderListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;


class EventServiceProvider extends ServiceProvider
{

    protected $listen = [
        
        ActivatedPluginEvent::class => [
            SkipLicenseReminderListener::class,
        ],
    ];

    public function boot(): void
    {
        $events = $this->app['events'];
        $events->listen(RouteMatched::class, function () {
            $this->app->extend('core.middleware', function ($middleware) {
                // Filter out the middleware you want to remove
                $filteredMiddleware = array_filter($middleware, function ($class) {
                    return $class !== EnsureLicenseHasBeenActivated::class;
                });
                return $filteredMiddleware;
            });
        });
    }

}