@php
    $countries = get_all_countries();
    $brands = get_all_brands();
    $categories = get_all_distributor_categories();
@endphp
<div class="distributor-filters-wrapper">
    <div class="bb-distributor-filter">
        {{-- <h4 class="bb-distributor-filter-title">{{ __('Country') }}</h4> --}}
        <div class="bb-distributor-filter-content">
            <select name="country" class="form-control form-select">
                {{-- <option value="">{{ __('Select Country') }}</option> --}}
                @foreach ($countries as $code => $country)
                    <option value="{{ $code }}" {{ $code == request()->input('country') ? 'selected' : '' }}>{{ $country }}</option>
                @endforeach
            </select>
        </div>
    </div>

    <div class="bb-distributor-filter">
        {{-- <h4 class="bb-distributor-filter-title">{{ __('Brand') }}</h4> --}}
        <div class="bb-distributor-filter-content">
            <select name="brand_id" class="form-control form-select">
                <option value="">{{ __('Select Brand') }}</option>
                @foreach ($brands as $brand)
                    <option value="{{ $brand->id }}" {{ $brand->id == request()->input('brand') ? 'selected' : '' }}>{{ $brand->name }}</option>
                @endforeach
            </select>
        </div>
    </div>

    <div class="bb-distributor-filter">
        {{-- <h4 class="bb-distributor-filter-title">{{ __('Search') }}</h4> --}}
        <div class="bb-distributor-filter-content">
            <input type="text" name="keyword" class="form-control" placeholder="{{ __('Search..') }}">
        </div>
    </div>
</div>

<div class="distributor-filters-categories-wrapper">
    <div class="category-carousel-container">
        <button class="nav-btn prev-btn" id="prevBtn">
            <i class="fas fa-chevron-left"></i>
        </button>

        <div class="category-carousel" id="categoryCarousel">
            @foreach ($categories as $category)
            <div class="category-item {{ $category->id == request()->input('category_id') ? 'active' : '' }}">
                <input type="radio" id="category_{{ $category->id }}" name="category_id" value="{{ $category->id }}" {{ $category->id == request()->input('category_id') ? 'checked' : '' }} class="category-radio">
                <label for="category_{{ $category->id }}" class="category-label">
                    <div class="category-icon"><img src="{{ RvMedia::getImageUrl($category->icon_image) }}"></div>
                    <span class="category-name">{{ $category->name }}</span>
                </label>
            </div>
            @endforeach


        </div>

        <button class="nav-btn next-btn" id="nextBtn">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
</div>

