<?php

namespace Botble\Marketplace\Models;

use Botble\Base\Casts\SafeContent;
use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DistributorCategory extends BaseModel
{
    protected $table = 'mp_distributor_categories';

    protected $fillable = [
        'name',
        'parent_id',
        'status',
        'icon',
        'icon_image',
        'image',
        'is_featured',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'is_featured' => 'boolean',
    ];

    public function distributors(): HasMany
    {
        return $this->hasMany(Distributor::class, 'category_id');
    }
    public function parent(): BelongsTo
    {
        return $this->belongsTo(DistributorCategory::class, 'parent_id')->withDefault();
    }
}
