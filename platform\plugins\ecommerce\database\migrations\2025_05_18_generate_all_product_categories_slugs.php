<?php

use Botble\Ecommerce\Models\ProductCategory;
use Botble\Slug\Facades\SlugHelper;
use Illuminate\Database\Migrations\Migration;

return new class() extends Migration
{
    public function up(): void
    {
        try {

            foreach (ProductCategory::query()->get() as $category) {
                /**
                 * @var ProductCategory $category
                 */
                SlugHelper::createSlug($category, $category->name);
            }
        } catch (Throwable $exception) {
            info('Error saving product category slugs: ' . $exception->getMessage());
        }
    }
};
