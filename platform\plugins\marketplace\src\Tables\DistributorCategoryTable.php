<?php

namespace Bo<PERSON><PERSON>\Marketplace\Tables;

use Bo<PERSON>ble\Marketplace\Models\DistributorCategory;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class DistributorCategoryTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(DistributorCategory::class)
            ->addHeaderAction(CreateHeaderAction::make()->route('marketplace.distributor-category.create'))
            ->addActions([
                EditAction::make()->route('marketplace.distributor-category.edit'),
                DeleteAction::make()->route('marketplace.distributor-category.destroy'),
            ])
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('marketplace.distributor-category.edit'),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('marketplace.distributor-category.destroy'),
            ])
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                $query->select([
                    'id',
                    'name',
                    'created_at',
                    'status',
                ]);
            });
    }
}
