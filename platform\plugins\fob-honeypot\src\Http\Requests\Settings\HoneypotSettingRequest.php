<?php

namespace FriendsOfBotble\Honeypot\Http\Requests\Settings;

use Bo<PERSON>ble\Base\Rules\OnOffRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use FriendsOfBotble\Honeypot\Facades\Honeypot;

class HoneypotSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            Honeypot::getSettingKey('enabled') => [new OnOffRule()],
            Honeypot::getSettingKey('amount_of_seconds') => ['nullable', 'integer', 'min:1'],
            Honeypot::getSettingKey('show_disclaimer') => [new OnOffRule()],
            ...$this->getFormRules(),
        ];
    }

    protected function getFormRules(): array
    {
        $rules = [];

        foreach (array_keys(Honeypot::getForms()) as $form) {
            $rules[Honeypot::getFormSettingKey($form)] = [new OnOffRule()];
        }

        return $rules;
    }
}
