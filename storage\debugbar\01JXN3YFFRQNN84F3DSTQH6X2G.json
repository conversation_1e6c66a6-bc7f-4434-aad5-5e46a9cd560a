{"__meta": {"id": "01JXN3YFFRQNN84F3DSTQH6X2G", "datetime": "2025-06-13 17:03:39", "utime": **********.00173, "method": "POST", "uri": "/admin/tools/data-synchronize/import/stores/import", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.048478, "end": **********.001751, "duration": 10.953273057937622, "duration_str": "10.95s", "measures": [{"label": "Booting", "start": **********.048478, "relative_start": 0, "end": **********.813252, "relative_end": **********.813252, "duration": 0.****************, "duration_str": "765ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.813263, "relative_start": 0.****************, "end": **********.001753, "relative_end": 2.1457672119140625e-06, "duration": 10.***************, "duration_str": "10.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.83414, "relative_start": 0.****************, "end": **********.845295, "relative_end": **********.845295, "duration": 0.011154890060424805, "duration_str": "11.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.997333, "relative_start": 10.**************, "end": **********.999076, "relative_end": **********.999076, "duration": 0.0017428398132324219, "duration_str": "1.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 1047, "nb_visible_statements": 500, "nb_excluded_statements": 547, "nb_failed_statements": 0, "accumulated_duration": 0.9603200000000002, "accumulated_duration_str": "960ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.8598442, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.046}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.866623, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.046, "width_percent": 0.024}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.3817348, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.07, "width_percent": 0.155}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.385509, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.225, "width_percent": 0.3}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('VOLVO', '<EMAIL>', '', '', '', '', '', '', '', null, '', '', 'v.png', null, null, null, null, 1, 'pending', '2025-06-13 17:03:29', '2025-06-13 17:03:29')", "type": "query", "params": [], "bindings": ["VOLVO", "<EMAIL>", "", "", "", "", "", "", "", null, "", "", "v.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", "2025-06-13 17:03:29"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3966658, "duration": 0.006900000000000001, "duration_str": "6.9ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 0.525, "width_percent": 0.719}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.474376, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.243, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.476472, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.293, "width_percent": 0.081}, {"sql": "update `mp_stores` set `name` = 'Komatsu', `logo` = 'k.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "k.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4858, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 1.375, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.4938161, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.847, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.495686, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.897, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = 'HYUNDAI', `logo` = 'hy.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["HYUNDAI", "hy.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.504239, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 1.949, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.511681, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.384, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.513289, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.429, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'Caterpillar', `logo` = 'c.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Caterpillar", "c.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.521562, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 2.483, "width_percent": 0.457}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.529164, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.94, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.5309, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.992, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = 'DOOSAN', `logo` = 'doosan-logo.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["DOOSAN", "doosan-logo.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.540491, "duration": 0.00509, "duration_str": "5.09ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 3.049, "width_percent": 0.53}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.548795, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.579, "width_percent": 0.037}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.550279, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.617, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = '<PERSON>', `logo` = 'm.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Mercedes", "m.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.563164, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 3.688, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.5708241, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.144, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.5726569, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.19, "width_percent": 0.078}, {"sql": "update `mp_stores` set `name` = 'Scania', `logo` = 's.png', `logo_square` = 'dhims.jpg', `content` = '<p>scania details</p>', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Scania", "s.png", "dhims.jpg", "<p>scania details</p>", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.583843, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 4.268, "width_percent": 0.464}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.597059, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.733, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.598676, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.775, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = 'JCB', `logo` = 'jcb.png', `logo_square` = null, `content` = '', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["JCB", "jcb.png", null, "", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.612699, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 4.831, "width_percent": 0.457}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.621175, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.288, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.623077, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.334, "width_percent": 0.093}, {"sql": "update `mp_stores` set `name` = 'MAN', `logo` = 'man.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["MAN", "man.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.632152, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 5.426, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.640636, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.878, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6425989, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.939, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = 'TATA', `logo` = 'tata.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["TATA", "tata.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6511772, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 5.997, "width_percent": 0.471}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.659337, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.468, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6612048, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.518, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = 'Daewoo', `logo` = 'daewoo.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "daewoo.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.669802, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 6.58, "width_percent": 0.435}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.677517, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.015, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.679447, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.067, "width_percent": 0.06}, {"sql": "update `ec_products` set `status` = 'published', `ec_products`.`updated_at` = '2025-06-13 17:03:29' where `ec_products`.`store_id` = 63 and `ec_products`.`store_id` is not null and `status` = 'blocked'", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63, "blocked"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 93}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 25, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 26, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.69011, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Store.php:93", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=93", "ajax": false, "filename": "Store.php", "line": "93"}, "connection": "muhrak", "explain": null, "start_percent": 7.128, "width_percent": 0.094}, {"sql": "update `mp_stores` set `name` = 'HITACHI', `logo` = 'icons/hitachi-logo.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["HITACHI", "icons/hitachi-logo.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.694584, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 7.222, "width_percent": 0.42}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7020972, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.641, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.70389, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.688, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = 'Siemens', `logo` = 'siemens.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Siemens", "siemens.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.713551, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 7.743, "width_percent": 0.425}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.721026, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.168, "width_percent": 0.032}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.722795, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.2, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = 'General Electric', `logo` = 'ge.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["General Electric", "ge.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7319522, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 8.267, "width_percent": 0.433}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7396948, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.7, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.741568, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.742, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'Honeywell International', `logo` = 'hwl.png', `logo_square` = 'eriezmetaldetectorpharmaceutical.jpg', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Honeywell International", "hwl.png", "eriezmetaldetectorpharmaceutical.jpg", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.75137, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 8.795, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7597861, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.24, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7616699, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.293, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = 'ABB', `logo` = 'abb.png', `logo_square` = null, `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["ABB", "abb.png", null, {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.770204, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 9.353, "width_percent": 0.449}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.778428, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.802, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7803042, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.851, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = 'Mitsubishi Electric', `logo` = 'mitsubishi-electric.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Mitsubishi Electric", "mitsubishi-electric.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.789342, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 9.915, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.796922, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.378, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.798429, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.417, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = 'Schneider Electric', `logo` = 'schneider-electric.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Schneider Electric", "schneider-electric.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.808711, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 10.474, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8164482, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.933, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.818197, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.979, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = 'Emerson Electric Co', `logo` = 'emerson-electric-co.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Emerson Electric Co", "emerson-electric-co.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.82954, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 11.03, "width_percent": 0.442}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.837354, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.471, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.839229, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.521, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = 'LG Electronics', `logo` = 'lg.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["LG Electronics", "lg.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.848432, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 11.591, "width_percent": 0.455}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.856703, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.046, "width_percent": 0.083}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.858995, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.129, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = 'Samsung Electronics', `logo` = 'sam.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Samsung Electronics", "sam.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.868652, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 12.191, "width_percent": 0.439}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8766031, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.63, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8784308, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.682, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = 'Bosch Group', `logo` = 'bosch-group.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Bosch Group", "bosch-group.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.886714, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 12.743, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8944478, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.195, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.896067, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.239, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = 'RS Pro', `logo` = 'icons/rs.webp', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["RS Pro", "icons/rs.webp", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.90458, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 13.299, "width_percent": 0.45}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.912158, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.749, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.913775, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.797, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = 'MKS HYDRAULIC', `logo` = 'logo.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["MKS HYDRAULIC", "logo.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.922807, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 13.849, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.9300508, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.283, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.931746, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.322, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = 'Guangzhou Jiajue Machinery Equipment', `logo` = '305480112-518175496979076-6001249714432641489-n.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["Guangzhou Jiajue Machinery Equipment", "305480112-518175496979076-6001249714432641489-n.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9446058, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 14.379, "width_percent": 0.444}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.952359, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.822, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.953914, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.867, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = 'ComeSys', `logo` = 'selection-113.png', `content` = '<p>Since its establishment in 1990, ComSys has provided solutions for Mobile and Industrial Applications. These include forklifts, construction equipment, agricultural machinery, buses, golf carts, Harbor tractors, ships, and special vehicles. </p><p> </p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["ComeSys", "selection-113.png", "<p>Since its establishment in 1990, ComSys has provided solutions for Mobile and Industrial Applications. These include forklifts, construction equipment, agricultural machinery, buses, golf carts, Harbor tractors, ships, and special vehicles. </p><p> </p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9683769, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 14.925, "width_percent": 0.465}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.977412, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.391, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.979285, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.444, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = 'I-Tork', `logo` = 'i-trok-accutators-uae.png', `content` = '<p><strong>I-Tork</strong> is a company specializing in the design and manufacture of industrial valve automation products, including electric and pneumatic actuators, limit switches, and various accessories for valve control. The company is known for its high-quality products, which are used in various industries, such as oil and gas, water treatment, petrochemical, power generation.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:29' where `id` = 63", "type": "query", "params": [], "bindings": ["I-Tork", "i-trok-accutators-uae.png", "<p><strong>I-Tork</strong> is a company specializing in the design and manufacture of industrial valve automation products, including electric and pneumatic actuators, limit switches, and various accessories for valve control. The company is known for its high-quality products, which are used in various industries, such as oil and gas, water treatment, petrochemical, power generation.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:29", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.989744, "duration": 0.00615, "duration_str": "6.15ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 15.504, "width_percent": 0.64}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.9999099, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.145, "width_percent": 0.037}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.001606, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.182, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'Liming', `logo` = 'liming-logo2.png', `logo_square` = 'aboutlm71500x700.jpg', `description` = 'LI MING MACHINERY CO., LTD. is specialist in design, manufacturing of wide range of high-tech speed reduction motor and helical gear reducers, worm gear reducers and planetary gear reducers. In recent', `content` = '<p>Li-Ming Machinery Co., Ltd. (also known as Liming Machinery) is a well-established company specializing in the production of precision gear reducers and power transmission equipment.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:30' where `id` = 63", "type": "query", "params": [], "bindings": ["Liming", "liming-logo2.png", "aboutlm71500x700.jpg", "LI MING MACHINERY CO., LTD. is specialist in design, manufacturing of wide range of high-tech speed reduction motor and helical gear reducers, worm gear reducers and planetary gear reducers. In recent", "<p>Li-Ming Machinery Co., Ltd. (also known as Liming Machinery) is a well-established company specializing in the production of precision gear reducers and power transmission equipment.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:30", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.01807, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 16.235, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.028683, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.695, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.030639, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.749, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = '<PERSON>rlekar Precision', `logo` = 'hirlekar-1.png', `logo_square` = 'hkk.png', `description` = 'Since 1974 Hirlekar Precision was started in the year 1974 by the Late <PERSON><PERSON> as a manufacturer of world class gauge sector mechanisms used in the instrumentation industry. Starting off in', `content` = '', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:30' where `id` = 63", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> Precision", "hirlekar-1.png", "hkk.png", "Since 1974 <PERSON><PERSON>kar Precision was started in the year 1974 by the Late <PERSON><PERSON> as a manufacturer of world class gauge sector mechanisms used in the instrumentation industry. Starting off in", "", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:30", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.044215, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 16.812, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.053757, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.257, "width_percent": 0.035}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.055398, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.292, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = 'KTNF', `logo` = 'ci.png', `logo_square` = 'innovative-it-infrastructure-solution-provider-with-global-competitiveness.png?v=**********', `description` = 'Innovative IT Infrastructure Solution Provider with Global Competitiveness', `content` = '<p>Innovative IT Infrastructure Solution Provider with Global Competitiveness</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:30' where `id` = 63", "type": "query", "params": [], "bindings": ["KTNF", "ci.png", "innovative-it-infrastructure-solution-provider-with-global-competitiveness.png?v=**********", "Innovative IT Infrastructure Solution Provider with Global Competitiveness", "<p>Innovative IT Infrastructure Solution Provider with Global Competitiveness</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:30", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0682569, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 17.361, "width_percent": 0.442}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.082863, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.802, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0846741, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.848, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = 'KP Electric Co., Ltd', `logo` = 'kpp.png', `logo_square` = 'kp-electric-co-ltd.jpg?v=**********', `description` = 'provider of electrical and power solutions, design, manufacturing', `content` = '<p>provider of electrical and power solutions, design, manufacturing</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:30' where `id` = 63", "type": "query", "params": [], "bindings": ["KP Electric Co., Ltd", "kpp.png", "kp-electric-co-ltd.jpg?v=**********", "provider of electrical and power solutions, design, manufacturing", "<p>provider of electrical and power solutions, design, manufacturing</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:30", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.098586, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 17.915, "width_percent": 0.559}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.110191, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.474, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 236}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.112015, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.523, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = 'KT&amp;C', `logo` = 'ktc.png', `logo_square` = 'thumb-16580-1.png?v=1730924790', `description` = 'Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras an', `content` = '<p><strong>KT&amp;C</strong> (Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras and video systems.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:03:30' where `id` = 63", "type": "query", "params": [], "bindings": ["KT&amp;C", "ktc.png", "thumb-16580-1.png?v=1730924790", "Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras an", "<p><strong>KT&amp;C</strong> (Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras and video systems.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:03:30", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.124795, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "ToolsStoreImporter.php:267", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FToolsStoreImporter.php&line=267", "ajax": false, "filename": "ToolsStoreImporter.php", "line": "267"}, "connection": "muhrak", "explain": null, "start_percent": 18.585, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 222}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/ToolsStoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\ToolsStoreImporter.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.1345701, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 19.058, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.136135, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.104, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.608399, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.156, "width_percent": 0.481}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6179311, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.637, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618969, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.685, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631478, "duration": 0.004900000000000001, "duration_str": "4.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.736, "width_percent": 0.51}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6436582, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.246, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6447031, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.294, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6567218, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.346, "width_percent": 0.463}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665838, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.81, "width_percent": 0.039}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6666892, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.848, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679218, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.913, "width_percent": 0.657}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691375, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.57, "width_percent": 0.065}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692603, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.634, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706451, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.687, "width_percent": 0.457}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717003, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.144, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718142, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.195, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.731434, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.255, "width_percent": 0.472}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742541, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.727, "width_percent": 0.076}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743902, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.803, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755738, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.861, "width_percent": 0.44}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764298, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.302, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765224, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.343, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.782588, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.393, "width_percent": 0.461}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.794321, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.855, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7954369, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.909, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8072782, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.967, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.81599, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.419, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817014, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.468, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.829263, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.528, "width_percent": 0.455}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838072, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.983, "width_percent": 0.036}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838994, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.02, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.85757, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.092, "width_percent": 0.477}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.86744, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.569, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.868518, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.62, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.881694, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.673, "width_percent": 0.455}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.890612, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.128, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.891864, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.186, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903696, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.24, "width_percent": 0.468}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9126601, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.708, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.913748, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.76, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925928, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.816, "width_percent": 0.43}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9347591, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.246, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935765, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.296, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947746, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.348, "width_percent": 0.431}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956327, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.779, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957511, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.819, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9695232, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.893, "width_percent": 0.43}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978482, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.323, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979557, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.377, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.991336, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.433, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.0002582, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.889, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.0013402, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.936, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.015852, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.988, "width_percent": 0.58}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.0269032, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.568, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.027936, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.617, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.039546, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.669, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.0483, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.132, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.049263, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.177, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.065028, "duration": 0.00587, "duration_str": "5.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.235, "width_percent": 0.611}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.076004, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.846, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.0770211, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.893, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.088018, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.946, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.096904, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.391, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.097905, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.438, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.42441, "duration": 0.004719999999999999, "duration_str": "4.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.492, "width_percent": 0.492}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.433574, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.983, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.434545, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.026, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.4516191, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.079, "width_percent": 0.485}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.4656641, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.564, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.4666579, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.612, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.482826, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.669, "width_percent": 0.465}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.492534, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.135, "width_percent": 0.071}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.4938629, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.206, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.505119, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.262, "width_percent": 0.455}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.514264, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.717, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.5152671, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.766, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.526338, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.819, "width_percent": 0.454}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.534256, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.273, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.535342, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.323, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.550757, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.378, "width_percent": 0.479}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.5635688, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.857, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.564848, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.918, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.575966, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.976, "width_percent": 0.482}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.5846841, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.458, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.585736, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.51, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.59646, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.564, "width_percent": 0.457}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.604476, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.021, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.6054451, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.064, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.6163461, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.114, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.6241221, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.551, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.625423, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.599, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.636529, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.663, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.644854, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.063, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.645989, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.114, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.657069, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.173, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.672071, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.625, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.67312, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.674, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.693425, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.726, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.701424, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.16, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.702348, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.2, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.713257, "duration": 0.004690000000000001, "duration_str": "4.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.253, "width_percent": 0.488}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.721619, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.741, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.722548, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.782, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.734745, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.837, "width_percent": 0.42}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.745276, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.257, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.746238, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.3, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.7587729, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.355, "width_percent": 0.501}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.768081, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.855, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.769475, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.912, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.787515, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.974, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.795679, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.419, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.796753, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.469, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.8071558, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.524, "width_percent": 0.446}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.8150592, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.97, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.81615, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.019, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.826568, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.069, "width_percent": 0.422}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.834665, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.49, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.835659, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.54, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.8464181, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.59, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.854636, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.063, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.855585, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.107, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.870209, "duration": 0.00625, "duration_str": "6.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.158, "width_percent": 0.651}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.883708, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.809, "width_percent": 0.066}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.884973, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.874, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.895864, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.927, "width_percent": 0.426}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.9038248, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.353, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.904778, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.398, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.9159489, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.453, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.923871, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.898, "width_percent": 0.059}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.925148, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.957, "width_percent": 0.095}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.936655, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.052, "width_percent": 0.431}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.944906, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.483, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.945987, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.538, "width_percent": 0.049}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.956994, "duration": 0.00467, "duration_str": "4.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.587, "width_percent": 0.486}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.965547, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.074, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.966649, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.125, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.982408, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.19, "width_percent": 0.459}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.990734, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.649, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834211.992328, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.699, "width_percent": 0.079}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.005222, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.778, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.014473, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.23, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.0155141, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.279, "width_percent": 0.049}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.029345, "duration": 0.00479, "duration_str": "4.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.328, "width_percent": 0.499}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.0386271, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.827, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.039612, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.875, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.053112, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.933, "width_percent": 0.5}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.0630598, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.433, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.0647402, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.513, "width_percent": 0.083}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.083863, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.597, "width_percent": 0.489}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.093834, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.086, "width_percent": 0.079}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.0952609, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.165, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.1104429, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.226, "width_percent": 0.495}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.119887, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.72, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.120871, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.767, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.1331558, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.819, "width_percent": 0.536}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.1431062, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.355, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.1440992, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.401, "width_percent": 0.046}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.156522, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.447, "width_percent": 0.531}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.166303, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.978, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.167572, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.034, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.179622, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.103, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.188356, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.54, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.1894102, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.596, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.2028031, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.66, "width_percent": 0.471}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.213698, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.131, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.214845, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.187, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.2321439, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.251, "width_percent": 0.428}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.241065, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.679, "width_percent": 0.059}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.242306, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.738, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.2550662, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.807, "width_percent": 0.454}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.267847, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.261, "width_percent": 0.071}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.269092, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.332, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.281683, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.383, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.290625, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.845, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.291992, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.892, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.308645, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.952, "width_percent": 0.451}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.318008, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.403, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.319095, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.461, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.3310912, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.518, "width_percent": 0.449}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.340145, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.966, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.341245, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.014, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.353494, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.067, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.362735, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.54, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.363765, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.592, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.377441, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.647, "width_percent": 0.493}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.386807, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.14, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.387948, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.195, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.399942, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.254, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.409988, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.715, "width_percent": 0.082}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.411426, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.797, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.423208, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.854, "width_percent": 0.472}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.432294, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.326, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.433421, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.388, "width_percent": 0.074}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.446105, "duration": 0.00513, "duration_str": "5.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.462, "width_percent": 0.534}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.4555802, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.997, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.456584, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.042, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.479433, "duration": 0.00456, "duration_str": "4.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.109, "width_percent": 0.475}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.49568, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.584, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.49689, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.639, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.509111, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.704, "width_percent": 0.477}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.518301, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.181, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.519477, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.237, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.5333889, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.295, "width_percent": 0.464}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.543964, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.76, "width_percent": 0.077}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.545305, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.837, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.557979, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.891, "width_percent": 0.453}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.566829, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.344, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.568072, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.405, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.5809531, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.472, "width_percent": 0.454}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.590291, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.926, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.591252, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.968, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.604655, "duration": 0.026019999999999998, "duration_str": "26.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.021, "width_percent": 2.71}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.6356492, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.73, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.636651, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.777, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.649431, "duration": 0.01199, "duration_str": "11.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.834, "width_percent": 1.249}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.670311, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.083, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.6716979, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.143, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.6939318, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.2, "width_percent": 0.465}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.70942, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.666, "width_percent": 0.093}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.711186, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.759, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.723403, "duration": 0.0049900000000000005, "duration_str": "4.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.817, "width_percent": 0.52}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.7372391, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.337, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.738239, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.387, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.754271, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.441, "width_percent": 0.423}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.766771, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.863, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.767993, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.921, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.785083, "duration": 0.02163, "duration_str": "21.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.98, "width_percent": 2.252}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.815658, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.232, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.81671, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.288, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.829162, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.357, "width_percent": 0.462}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.838034, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.82, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.838966, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.864, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.854295, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.922, "width_percent": 0.431}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.865138, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.353, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.866175, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.406, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.879216, "duration": 0.00595, "duration_str": "5.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.464, "width_percent": 0.62}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.8897781, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.084, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.8909302, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.134, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.9053512, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.201, "width_percent": 0.435}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.915178, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.636, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.916271, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.685, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.932889, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.739, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.944721, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.176, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.94584, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.225, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.959758, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.28, "width_percent": 0.461}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.968795, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.742, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.96978, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.789, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.98194, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.852, "width_percent": 0.444}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.990704, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.296, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834212.991679, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.343, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.0042331, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.399, "width_percent": 0.615}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.0147028, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.014, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.015644, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.058, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.029996, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.123, "width_percent": 0.436}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.038613, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.559, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.039582, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.606, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.0522351, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.661, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.064769, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.095, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.066373, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.155, "width_percent": 0.073}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.0803442, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.228, "width_percent": 0.449}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.088993, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.677, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.0899801, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.726, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.106251, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.78, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.118736, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.225, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.119641, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.268, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.132647, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.322, "width_percent": 0.417}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.141427, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.738, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.1424508, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.788, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.15467, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.85, "width_percent": 0.424}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.1658928, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.274, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.166821, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.318, "width_percent": 0.071}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.183157, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.389, "width_percent": 0.458}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.1922278, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.847, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.1934052, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.891, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.2052832, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.96, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.2141018, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.394, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.215056, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.439, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.22865, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.495, "width_percent": 0.449}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.2373588, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.944, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.2382998, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.988, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.251112, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.048, "width_percent": 0.483}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.260545, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.531, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.261783, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.594, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.273367, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.65, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.282284, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.087, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.2833438, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.14, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.296018, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.203, "width_percent": 0.426}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.304494, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.629, "width_percent": 0.036}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.305348, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.665, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.321254, "duration": 0.004860000000000001, "duration_str": "4.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.719, "width_percent": 0.506}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.3307261, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.225, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.331752, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.276, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.343304, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.333, "width_percent": 0.428}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.351852, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.761, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.352792, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.804, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.368702, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.862, "width_percent": 0.446}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.3779461, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.307, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.378997, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.357, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.390363, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.413, "width_percent": 0.429}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.39889, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.843, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.399954, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.898, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.4129689, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.952, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.422093, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.396, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.423012, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.439, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.4353678, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.495, "width_percent": 0.429}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.4466, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.924, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.447684, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.979, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.459299, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.031, "width_percent": 0.432}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.4680629, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.463, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.4689791, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.503, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.484586, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.554, "width_percent": 0.438}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.495084, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.993, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.496145, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.044, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.5080621, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.104, "width_percent": 0.417}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.516471, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.521, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.517482, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.562, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.5330188, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.63, "width_percent": 0.427}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.5416172, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.057, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.542701, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.104, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.558641, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.163, "width_percent": 0.438}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.5700579, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.602, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.571215, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.662, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.588279, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.717, "width_percent": 0.406}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.604604, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.123, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.605598, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.171, "width_percent": 0.044}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.633836, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.215, "width_percent": 0.436}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.6449, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.651, "width_percent": 0.09}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.646778, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.741, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.659998, "duration": 0.00743, "duration_str": "7.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.811, "width_percent": 0.774}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.6720562, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.584, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.673048, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.632, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.6916502, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.684, "width_percent": 0.43}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.708713, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.114, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.7101321, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.163, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.731447, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.232, "width_percent": 0.421}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.7401018, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.653, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.741167, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.698, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.767521, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.748, "width_percent": 0.424}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.784086, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.171, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.785545, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.251, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.8032, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.305, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.8129818, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.698, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.8139381, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.74, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.831422, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.792, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.839806, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.192, "width_percent": 0.032}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.8406472, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.224, "width_percent": 0.046}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.854418, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.27, "width_percent": 0.445}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.8656182, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.715, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.866714, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.76, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.878392, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.829, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.887533, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.247, "width_percent": 0.039}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.888457, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.285, "width_percent": 0.046}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9086041, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.331, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9264078, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.765, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.927963, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.81, "width_percent": 0.111}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9409919, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.921, "width_percent": 0.408}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9498398, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.33, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9509668, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.374, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9777331, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.435, "width_percent": 0.523}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.9952848, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.958, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834213.996308, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.002, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.012939, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.052, "width_percent": 0.428}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.0221658, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.48, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.023231, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.525, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.036616, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.58, "width_percent": 0.383}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.044762, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.963, "width_percent": 0.075}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.046199, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.038, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.0687368, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.092, "width_percent": 0.464}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.077928, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.556, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.079366, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.603, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.101503, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.662, "width_percent": 0.42}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.110469, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.082, "width_percent": 0.035}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.1117098, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.117, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.131738, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.179, "width_percent": 0.473}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.143254, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.651, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.144352, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.698, "width_percent": 0.079}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.1569312, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.777, "width_percent": 0.433}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.166018, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.211, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.1670282, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.253, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.181357, "duration": 0.00462, "duration_str": "4.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.319, "width_percent": 0.481}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.1972158, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.8, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.198194, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.845, "width_percent": 0.048}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.21097, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.893, "width_percent": 0.427}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.222145, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.32, "width_percent": 0.037}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.2230358, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.357, "width_percent": 0.045}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.249573, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.402, "width_percent": 0.44}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.2585812, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.842, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.259652, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.888, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.2730289, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.949, "width_percent": 0.409}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.2856069, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.358, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.2865179, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.398, "width_percent": 0.043}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.302958, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.441, "width_percent": 0.422}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.3144832, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.863, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.31593, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.913, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.32812, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.97, "width_percent": 0.41}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.33666, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.38, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.337673, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.424, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.353521, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.488, "width_percent": 0.414}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.3630128, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.902, "width_percent": 0.045}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834214.364022, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.947, "width_percent": 0.053}, {"sql": "... 547 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Customer": {"value": 348, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 347, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 698, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/stores/import", "action_name": "tools.data-synchronize.import.stores.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\Tools\\ImportStoreController@import", "uri": "POST admin/tools/data-synchronize/import/stores/import", "permission": "marketplace.store.import", "controller": "Botble\\Marketplace\\Http\\Controllers\\Tools\\ImportStoreController@import<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/tools/data-synchronize/import/stores", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:70-118</a>", "middleware": "web, core, auth", "duration": "10.97s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1254194123 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1254194123\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1048460349 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"84 characters\">brands_export_2025-06-13_20-43-01-95526f89e1df6b46de04112ef99337a5-684c59e005a21.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>total</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048460349\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1827566286 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Iit4TENEaG4rUVJzUnpGUE5WcWtnamc9PSIsInZhbHVlIjoiK1locTEraE5VTU5Kc3dhQmJXSG9oR2trYnh1dk1POUNEZW1SYmRaTy84Y1ZvR2RXL1R3OXJQQXdJSGFkc3VlN05Da050amdtWjEybm5HMFhlVi9mTVV5MlpsMmp2MnZzN08xdUNYZUNac2ZHSmNndktmTnJQRTdWdmVmR3I5MS8iLCJtYWMiOiI3NmU4MWU2NjkwZTAzYjUxNmM2NjBjNDQ3NmViNTU5YWUzNGNiMWQ5ZWMwZjU2NTA4MTA0ODg3ZTU4MjFjMzg1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">741</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryXcyBBBnSRmsxgMRW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"60 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Iit4TENEaG4rUVJzUnpGUE5WcWtnamc9PSIsInZhbHVlIjoiK1locTEraE5VTU5Kc3dhQmJXSG9oR2trYnh1dk1POUNEZW1SYmRaTy84Y1ZvR2RXL1R3OXJQQXdJSGFkc3VlN05Da050amdtWjEybm5HMFhlVi9mTVV5MlpsMmp2MnZzN08xdUNYZUNac2ZHSmNndktmTnJQRTdWdmVmR3I5MS8iLCJtYWMiOiI3NmU4MWU2NjkwZTAzYjUxNmM2NjBjNDQ3NmViNTU5YWUzNGNiMWQ5ZWMwZjU2NTA4MTA0ODg3ZTU4MjFjMzg1IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlZRU0Z4R1U1VFVYK2NpSTVaQWVoeXc9PSIsInZhbHVlIjoicVQ2VWhVQnpxTUpwUFpkcU1tQTNwZElKbCtzVlNtaDMrQmE0REJiTWswdE5CZTRyZXR2R1RGWkN4VUFERy9HTHlKZGFsNkRuTzRRakVZbzMraC93UWZoZXUrdDh3cU1VQ3NxRk5qU21iWW1ZVWgrNGVuSElZYnFuV3RWNDhuSGgiLCJtYWMiOiJlMzQ1YzgyMjE2ODFjNTA1ZjBkZTJhNWVmNGVjYTk0YjY2ZTQyZTY5NmJiMTA0NWE1MmZiYWZiZjBiMTZiMDZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827566286\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-948845275 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:03:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948845275\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1725441710 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"60 characters\">https://muhrak.gc/admin/tools/data-synchronize/import/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725441710\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tools/data-synchronize/import/stores/import", "action_name": "tools.data-synchronize.import.stores.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\Tools\\ImportStoreController@import"}, "badge": null}}