@if ($distributors->isNotEmpty())
    <div class="row row-cols-1 row-cols-lg-2 g-4">
        @foreach ($distributors as $distributor)
        <div class="col">
            <div class="distributor-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="distributor-card-flag">
                            <img src="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.4.3/flags/4x3/{{ strtolower($distributor->country) }}.svg">
                        </div>
                        <div class="distributor-name">{{ $distributor->name }}</div>
                    </div>
                    <div class="distributor-logo">
                        <img src="{{ RvMedia::getImageUrl($distributor->logo, 'thumb', false, RvMedia::getDefaultImage()) }}" alt="{{ $distributor->name }}">
                    </div>
                </div>
                <div class="divider"></div>
                <a href="{{ $distributor->website }}" class="contact-info">
                    <i class="fas fa-home"></i>
                    {{ $distributor->website }}
                </a>
                <a href="tel:+97313114133" class="contact-info">
                    <i class="fas fa-phone"></i>
                    {{ $distributor->phone }}
                </a>
                <a href="mailto:{{ $distributor->email }}" class="contact-info">
                    <i class="fas fa-envelope"></i>
                    {{ $distributor->email }}
                </a>

            </div>
        </div>

        @endforeach
    </div>
@endif
