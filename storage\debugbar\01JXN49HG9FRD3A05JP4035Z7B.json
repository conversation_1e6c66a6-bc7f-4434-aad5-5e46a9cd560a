{"__meta": {"id": "01JXN49HG9FRD3A05JP4035Z7B", "datetime": "2025-06-13 17:09:41", "utime": **********.514191, "method": "DELETE", "uri": "/admin/marketplaces/stores/63", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749834579.997508, "end": **********.514209, "duration": 1.5167009830474854, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749834579.997508, "relative_start": 0, "end": **********.86221, "relative_end": **********.86221, "duration": 0.****************, "duration_str": "865ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.862222, "relative_start": 0.****************, "end": **********.514212, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "652ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.882508, "relative_start": 0.****************, "end": **********.89317, "relative_end": **********.89317, "duration": 0.010662078857421875, "duration_str": "10.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.969408, "relative_start": 0.****************, "end": **********.509869, "relative_end": **********.509869, "duration": 0.****************, "duration_str": "540ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 17, "nb_statements": 15, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019980000000000005, "accumulated_duration_str": "19.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.90887, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 2.352}, {"sql": "select * from `mp_stores` where `id` = '63' limit 1", "type": "query", "params": [], "bindings": ["63"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.914828, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.352, "width_percent": 6.156}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.922266, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.509, "width_percent": 2.152}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 114}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 887}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 170}], "start": **********.032846, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DeleteResourceAction.php:114", "source": {"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FHttp%2FActions%2FDeleteResourceAction.php&line=114", "ajax": false, "filename": "DeleteResourceAction.php", "line": "114"}, "connection": "muhrak", "explain": null, "start_percent": 10.661, "width_percent": 0}, {"sql": "delete from `mp_stores` where `id` = 63", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 887}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}], "start": **********.035397, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DeleteResourceAction.php:90", "source": {"index": 14, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FHttp%2FActions%2FDeleteResourceAction.php&line=90", "ajax": false, "filename": "DeleteResourceAction.php", "line": "90"}, "connection": "muhrak", "explain": null, "start_percent": 10.661, "width_percent": 2.603}, {"sql": "select * from `slugs` where `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `reference_id` = 63 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}], "start": **********.037251, "duration": 0.0076, "duration_str": "7.6ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.263, "width_percent": 38.038}, {"sql": "select * from `ec_products` where `ec_products`.`store_id` = 63 and `ec_products`.`store_id` is not null order by `ec_products`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 28, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}], "start": **********.049429, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 51.301, "width_percent": 3.954}, {"sql": "delete from `ec_discounts` where `ec_discounts`.`store_id` = 63 and `ec_discounts`.`store_id` is not null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 71}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 887}], "start": **********.051785, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Store.php:71", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=71", "ajax": false, "filename": "Store.php", "line": "71"}, "connection": "muhrak", "explain": null, "start_percent": 55.255, "width_percent": 5.255}, {"sql": "update `ec_orders` set `store_id` = null, `ec_orders`.`updated_at` = '2025-06-13 17:09:41' where `ec_orders`.`store_id` = 63 and `ec_orders`.`store_id` is not null and `is_finished` = 1", "type": "query", "params": [], "bindings": [null, "2025-06-13 17:09:41", 63, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 72}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 90}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 887}], "start": **********.053984, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Store.php:72", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=72", "ajax": false, "filename": "Store.php", "line": "72"}, "connection": "muhrak", "explain": null, "start_percent": 60.511, "width_percent": 7.307}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 63 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 95}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 147}], "start": **********.060452, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 67.818, "width_percent": 3.954}, {"sql": "delete from `meta_boxes` where (`reference_id` = 63 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store')", "type": "query", "params": [], "bindings": [63, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}], "start": **********.1313941, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:25", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FListeners%2FDeletedContentListener.php&line=25", "ajax": false, "filename": "DeletedContentListener.php", "line": "25"}, "connection": "muhrak", "explain": null, "start_percent": 71.772, "width_percent": 3.253}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 63 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store')", "type": "query", "params": [], "bindings": ["seo_meta", 63, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 189}, {"index": 16, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}], "start": **********.273589, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 75.025, "width_percent": 2.703}, {"sql": "delete from `slugs` where (`reference_id` = 63 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store')", "type": "query", "params": [], "bindings": [63, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}], "start": **********.301806, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:18", "source": {"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FDeletedContentListener.php&line=18", "ajax": false, "filename": "DeletedContentListener.php", "line": "18"}, "connection": "muhrak", "explain": null, "start_percent": 77.728, "width_percent": 4.454}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 963}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.324404, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 82.182, "width_percent": 3.654}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'store', 'deleted', 1, 1, 63, 'MENTPACK', 'danger', '2025-06-13 17:09:41', '2025-06-13 17:09:41', '{\\\"_method\\\":\\\"delete\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "store", "deleted", 1, 1, 63, "MENTPACK", "danger", "2025-06-13 17:09:41", "2025-06-13 17:09:41", "{\"_method\":\"delete\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}], "start": **********.4129448, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "muhrak", "explain": null, "start_percent": 85.836, "width_percent": 11.812}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'faq_schema_config' and `reference_id` = 63 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store')", "type": "query", "params": [], "bindings": ["faq_schema_config", 63, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/plugins/faq/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\faq\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 92}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 118}], "start": **********.4585562, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 97.648, "width_percent": 2.352}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 122}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 902}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 887}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 170}], "start": **********.508831, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "DeleteResourceAction.php:122", "source": {"index": 9, "namespace": null, "name": "platform/core/base/src/Http/Actions/DeleteResourceAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Actions\\DeleteResourceAction.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FHttp%2FActions%2FDeleteResourceAction.php&line=122", "ajax": false, "filename": "DeleteResourceAction.php", "line": "122"}, "connection": "muhrak", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/63", "action_name": "marketplace.store.destroy", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@destroy", "uri": "DELETE admin/marketplaces/stores/{store}", "controller": "Botble\\Marketplace\\Http\\Controllers\\StoreController@destroy<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=175\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=175\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/marketplace/src/Http/Controllers/StoreController.php:175-178</a>", "middleware": "web, core, auth", "duration": "1.51s", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-335285310 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-335285310\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2065339242 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065339242\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1179151956 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkdMbisrNzFBUXBHSGZMMUFqQWh5dXc9PSIsInZhbHVlIjoiREFLTnF1V1FiRStKNVhiRENxUjV1RjFadzFPVUpnZTREdU9sNTdDMW1Oa1lMWTZ3UGNHengyeUVuTUVrWnUydkxMQ0JYQkI5VnZlWllSUTlDSjJhVXN3aWdlSlJCUCtOWlUxT1kzZ2xleHQwV0hldUxKOSthWkl2ajVycTJlL3oiLCJtYWMiOiIyMzE5ODI0ZjY2NDRhNGU5MzkzOTQwMGU1MTg0ZjM4ZTNkZTk0YzUxZWZkNTdkZDRmMTU5MGNjNTc5MzI3MjE5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkdMbisrNzFBUXBHSGZMMUFqQWh5dXc9PSIsInZhbHVlIjoiREFLTnF1V1FiRStKNVhiRENxUjV1RjFadzFPVUpnZTREdU9sNTdDMW1Oa1lMWTZ3UGNHengyeUVuTUVrWnUydkxMQ0JYQkI5VnZlWllSUTlDSjJhVXN3aWdlSlJCUCtOWlUxT1kzZ2xleHQwV0hldUxKOSthWkl2ajVycTJlL3oiLCJtYWMiOiIyMzE5ODI0ZjY2NDRhNGU5MzkzOTQwMGU1MTg0ZjM4ZTNkZTk0YzUxZWZkNTdkZDRmMTU5MGNjNTc5MzI3MjE5IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InEvNSs1a0ZxQVg0VHRBVDBVanV0RUE9PSIsInZhbHVlIjoiTnpFNGFIRDltV3N4am9tbmJGQkRDYzZIQUdoRHlDQUkxWmYzVHVMcjU3K1g1eFFtbnErUVpYOHJaSWV4a0cxVXZ5K2gyS3hZczlzeHFKckRDZmVNVWRKZWFjbXlhZ2E3Q3FTY0cyNDhUa043SHg2RjE5MHMvN3FiV2FRR1ZHakIiLCJtYWMiOiI1ZmIyNTIxZDVjYTQxNjBkYjBhNTZmZjdhNGI2NmQyODg0Yjc4OTVjZmI5YjIxMDJmMzcwNDc2N2IwNjdhN2VlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179151956\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:09:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1458023976 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458023976\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/63", "action_name": "marketplace.store.destroy", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@destroy"}, "badge": null}}