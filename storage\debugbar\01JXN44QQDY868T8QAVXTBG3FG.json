{"__meta": {"id": "01JXN44QQDY868T8QAVXTBG3FG", "datetime": "2025-06-13 17:07:04", "utime": **********.046593, "method": "POST", "uri": "/admin/marketplaces/stores/import/import", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749834413.991518, "end": **********.046607, "duration": 10.055088996887207, "duration_str": "10.06s", "measures": [{"label": "Booting", "start": 1749834413.991518, "relative_start": 0, "end": **********.822196, "relative_end": **********.822196, "duration": 0.****************, "duration_str": "831ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.822207, "relative_start": 0.****************, "end": **********.046609, "relative_end": 1.9073486328125e-06, "duration": 9.***************, "duration_str": "9.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.842615, "relative_start": 0.****************, "end": **********.85587, "relative_end": **********.85587, "duration": 0.013255119323730469, "duration_str": "13.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.042078, "relative_start": 10.***************, "end": **********.044134, "relative_end": **********.044134, "duration": 0.0020558834075927734, "duration_str": "2.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 1047, "nb_visible_statements": 500, "nb_excluded_statements": 547, "nb_failed_statements": 0, "accumulated_duration": 0.9444700000000007, "accumulated_duration_str": "944ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.869798, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.057}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.8771439, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.057, "width_percent": 0.041}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.40099, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.098, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.403486, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.15, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = 'VOLVO', `logo` = 'v.png', `cover_image` = null, `description` = '', `content` = '', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["VOLVO", "v.png", null, "", "", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.416158, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 0.206, "width_percent": 0.429}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.4273572, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.635, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.429498, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.684, "width_percent": 0.073}, {"sql": "update `mp_stores` set `name` = 'Komatsu', `logo` = 'k.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "k.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.438565, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 0.757, "width_percent": 0.402}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.4462268, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.159, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.447957, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.205, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'HYUNDAI', `logo` = 'hy.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["HYUNDAI", "hy.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.457567, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 1.258, "width_percent": 0.39}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.465069, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.647, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.466908, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.706, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = 'Caterpillar', `logo` = 'c.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Caterpillar", "c.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.475429, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 1.765, "width_percent": 0.412}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.4825902, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.177, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.484327, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.226, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = 'DOOSAN', `logo` = 'doosan-logo.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["DOOSAN", "doosan-logo.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.494025, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 2.295, "width_percent": 0.414}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.501193, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.709, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.502926, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.759, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = '<PERSON>', `logo` = 'm.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Mercedes", "m.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5120041, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 2.811, "width_percent": 0.424}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.519298, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.235, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.520765, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.273, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = 'Scania', `logo` = 's.png', `logo_square` = 'dhims.jpg', `content` = '<p>scania details</p>', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Scania", "s.png", "dhims.jpg", "<p>scania details</p>", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.531663, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 3.328, "width_percent": 0.398}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.5398731, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.726, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.541473, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.774, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = 'JCB', `logo` = 'jcb.png', `logo_square` = null, `content` = '', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["JCB", "jcb.png", null, "", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.552434, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 3.832, "width_percent": 0.435}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.560504, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.267, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.562546, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.307, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = 'MAN', `logo` = 'man.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["MAN", "man.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5715241, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 4.376, "width_percent": 0.42}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.579292, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.796, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.58125, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.843, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = 'TATA', `logo` = 'tata.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["TATA", "tata.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5899918, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 4.904, "width_percent": 0.415}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.599735, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.319, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.601716, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.373, "width_percent": 0.072}, {"sql": "update `mp_stores` set `name` = 'Daewoo', `logo` = 'daewoo.png', `status` = 'pending', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "daewoo.png", {"value": "pending", "label": "Pending"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.61145, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 5.445, "width_percent": 0.406}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6187072, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.851, "width_percent": 0.066}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.620661, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.917, "width_percent": 0.095}, {"sql": "update `ec_products` set `status` = 'published', `ec_products`.`updated_at` = '2025-06-13 17:06:55' where `ec_products`.`store_id` = 63 and `ec_products`.`store_id` is not null and `status` = 'blocked'", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63, "blocked"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 93}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 25, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 26, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6317089, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Store.php:93", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=93", "ajax": false, "filename": "Store.php", "line": "93"}, "connection": "muhrak", "explain": null, "start_percent": 6.012, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = 'HITACHI', `logo` = 'icons/hitachi-logo.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["HITACHI", "icons/hitachi-logo.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.636244, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 6.073, "width_percent": 0.438}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6435492, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.512, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.645376, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.559, "width_percent": 0.093}, {"sql": "update `mp_stores` set `name` = 'Siemens', `logo` = 'siemens.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Siemens", "siemens.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.65467, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 6.652, "width_percent": 0.415}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.662229, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.067, "width_percent": 0.067}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6642199, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.134, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = 'General Electric', `logo` = 'ge.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["General Electric", "ge.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.673026, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 7.193, "width_percent": 0.421}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.68183, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.615, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.6837401, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.673, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = 'Honeywell International', `logo` = 'hwl.png', `logo_square` = 'eriezmetaldetectorpharmaceutical.jpg', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Honeywell International", "hwl.png", "eriezmetaldetectorpharmaceutical.jpg", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.693174, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 7.739, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.701724, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.133, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.703438, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.19, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = 'ABB', `logo` = 'abb.png', `logo_square` = null, `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["ABB", "abb.png", null, {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.714043, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 8.242, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.721464, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.636, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.723239, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.692, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = 'Mitsubishi Electric', `logo` = 'mitsubishi-electric.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Mitsubishi Electric", "mitsubishi-electric.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.732868, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 8.756, "width_percent": 0.399}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7399302, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.155, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7415538, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.207, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = 'Schneider Electric', `logo` = 'schneider-electric.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Schneider Electric", "schneider-electric.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.75079, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 9.271, "width_percent": 0.426}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7581758, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.696, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.759676, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.737, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'Emerson Electric Co', `logo` = 'emerson-electric-co.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Emerson Electric Co", "emerson-electric-co.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.768936, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 9.79, "width_percent": 0.413}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.7764611, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.203, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.77827, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.253, "width_percent": 0.069}, {"sql": "update `mp_stores` set `name` = 'LG Electronics', `logo` = 'lg.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["LG Electronics", "lg.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.790987, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 10.322, "width_percent": 0.414}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.79831, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.736, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.800314, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.784, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = 'Samsung Electronics', `logo` = 'sam.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Samsung Electronics", "sam.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.809529, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 10.841, "width_percent": 0.401}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.820405, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.242, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.822061, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.286, "width_percent": 0.048}, {"sql": "update `mp_stores` set `name` = 'Bosch Group', `logo` = 'bosch-group.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Bosch Group", "bosch-group.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.835054, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 11.333, "width_percent": 0.391}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8419518, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.724, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.843513, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.764, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = 'RS Pro', `logo` = 'icons/rs.webp', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["RS Pro", "icons/rs.webp", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.852741, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 11.819, "width_percent": 0.373}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.859605, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.192, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.861357, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.247, "width_percent": 0.075}, {"sql": "update `mp_stores` set `name` = 'MKS HYDRAULIC', `logo` = 'logo.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["MKS HYDRAULIC", "logo.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.870674, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 12.322, "width_percent": 0.41}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.8787618, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.732, "width_percent": 0.065}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.880797, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.797, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = 'Guangzhou Jiajue Machinery Equipment', `logo` = '305480112-518175496979076-6001249714432641489-n.png', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Guangzhou Jiajue Machinery Equipment", "305480112-518175496979076-6001249714432641489-n.png", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.88938, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 12.853, "width_percent": 0.426}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.898919, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.278, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.900836, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.332, "width_percent": 0.076}, {"sql": "update `mp_stores` set `name` = 'ComeSys', `logo` = 'selection-113.png', `content` = '<p>Since its establishment in 1990, ComSys has provided solutions for Mobile and Industrial Applications. These include forklifts, construction equipment, agricultural machinery, buses, golf carts, Harbor tractors, ships, and special vehicles. </p><p> </p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["ComeSys", "selection-113.png", "<p>Since its establishment in 1990, ComSys has provided solutions for Mobile and Industrial Applications. These include forklifts, construction equipment, agricultural machinery, buses, golf carts, Harbor tractors, ships, and special vehicles. </p><p> </p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.911711, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 13.409, "width_percent": 0.452}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.920738, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.861, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.922331, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.906, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'I-Tork', `logo` = 'i-trok-accutators-uae.png', `content` = '<p><strong>I-Tork</strong> is a company specializing in the design and manufacture of industrial valve automation products, including electric and pneumatic actuators, limit switches, and various accessories for valve control. The company is known for its high-quality products, which are used in various industries, such as oil and gas, water treatment, petrochemical, power generation.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["I-Tork", "i-trok-accutators-uae.png", "<p><strong>I-Tork</strong> is a company specializing in the design and manufacture of industrial valve automation products, including electric and pneumatic actuators, limit switches, and various accessories for valve control. The company is known for its high-quality products, which are used in various industries, such as oil and gas, water treatment, petrochemical, power generation.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.933519, "duration": 0.00746, "duration_str": "7.46ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 13.959, "width_percent": 0.79}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.945179, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.749, "width_percent": 0.064}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.947721, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.813, "width_percent": 0.067}, {"sql": "update `mp_stores` set `name` = 'Liming', `logo` = 'liming-logo2.png', `logo_square` = 'aboutlm71500x700.jpg', `description` = 'LI MING MACHINERY CO., LTD. is specialist in design, manufacturing of wide range of high-tech speed reduction motor and helical gear reducers, worm gear reducers and planetary gear reducers. In recent', `content` = '<p>Li-Ming Machinery Co., Ltd. (also known as Liming Machinery) is a well-established company specializing in the production of precision gear reducers and power transmission equipment.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["Liming", "liming-logo2.png", "aboutlm71500x700.jpg", "LI MING MACHINERY CO., LTD. is specialist in design, manufacturing of wide range of high-tech speed reduction motor and helical gear reducers, worm gear reducers and planetary gear reducers. In recent", "<p>Li-Ming Machinery Co., Ltd. (also known as Liming Machinery) is a well-established company specializing in the production of precision gear reducers and power transmission equipment.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.959825, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 14.879, "width_percent": 0.424}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.969212, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.303, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.970978, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.357, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = '<PERSON>rlekar Precision', `logo` = 'hirlekar-1.png', `logo_square` = 'hkk.png', `description` = 'Since 1974 Hirlekar Precision was started in the year 1974 by the Late <PERSON><PERSON> as a manufacturer of world class gauge sector mechanisms used in the instrumentation industry. Starting off in', `content` = '', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:55' where `id` = 63", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> Precision", "hirlekar-1.png", "hkk.png", "Since 1974 <PERSON><PERSON>kar Precision was started in the year 1974 by the Late <PERSON><PERSON> as a manufacturer of world class gauge sector mechanisms used in the instrumentation industry. Starting off in", "", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:55", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9884999, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 15.417, "width_percent": 0.416}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.002045, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.833, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.004294, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.905, "width_percent": 0.075}, {"sql": "update `mp_stores` set `name` = 'KTNF', `logo` = 'ci.png', `logo_square` = 'innovative-it-infrastructure-solution-provider-with-global-competitiveness.png?v=**********', `description` = 'Innovative IT Infrastructure Solution Provider with Global Competitiveness', `content` = '<p>Innovative IT Infrastructure Solution Provider with Global Competitiveness</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:56' where `id` = 63", "type": "query", "params": [], "bindings": ["KTNF", "ci.png", "innovative-it-infrastructure-solution-provider-with-global-competitiveness.png?v=**********", "Innovative IT Infrastructure Solution Provider with Global Competitiveness", "<p>Innovative IT Infrastructure Solution Provider with Global Competitiveness</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:56", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0178201, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 15.98, "width_percent": 0.456}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.027833, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.437, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0299652, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.49, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = 'KP Electric Co., Ltd', `logo` = 'kpp.png', `logo_square` = 'kp-electric-co-ltd.jpg?v=**********', `description` = 'provider of electrical and power solutions, design, manufacturing', `content` = '<p>provider of electrical and power solutions, design, manufacturing</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:56' where `id` = 63", "type": "query", "params": [], "bindings": ["KP Electric Co., Ltd", "kpp.png", "kp-electric-co-ltd.jpg?v=**********", "provider of electrical and power solutions, design, manufacturing", "<p>provider of electrical and power solutions, design, manufacturing</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:56", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.042472, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 16.56, "width_percent": 0.409}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.051997, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.968, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 267}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 19, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.053772, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.023, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = 'KT&amp;C', `logo` = 'ktc.png', `logo_square` = 'thumb-16580-1.png?v=1730924790', `description` = 'Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras an', `content` = '<p><strong>KT&amp;C</strong> (Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras and video systems.</p>', `status` = 'published', `mp_stores`.`updated_at` = '2025-06-13 17:06:56' where `id` = 63", "type": "query", "params": [], "bindings": ["KT&amp;C", "ktc.png", "thumb-16580-1.png?v=1730924790", "Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras an", "<p><strong>KT&amp;C</strong> (Korea Technology and Communications) is a company specializing in the design, manufacturing, and distribution of advanced security solutions, particularly in the field of surveillance cameras and video systems.</p>", {"value": "published", "label": "Approved"}, "2025-06-13 17:06:56", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 16, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.066829, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:298", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=298", "ajax": false, "filename": "StoreImporter.php", "line": "298"}, "connection": "muhrak", "explain": null, "start_percent": 17.076, "width_percent": 0.48}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.0769072, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 17.556, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.07867, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 17.604, "width_percent": 0.076}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.097792, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 17.68, "width_percent": 0.415}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.106052, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.095, "width_percent": 0.037}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.106905, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.132, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1196432, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.183, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.128076, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.583, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.129315, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.631, "width_percent": 0.095}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.141285, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.726, "width_percent": 0.398}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.150332, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.124, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.151474, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.176, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1634421, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.233, "width_percent": 0.442}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.172009, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.675, "width_percent": 0.037}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.172853, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.712, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1850631, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.769, "width_percent": 0.425}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193368, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.193, "width_percent": 0.036}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194259, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.229, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.206184, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.284, "width_percent": 0.409}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215655, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.693, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.216728, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.748, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2294211, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 20.801, "width_percent": 0.444}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.237968, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.245, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.238857, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.286, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2509968, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.337, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.259265, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.755, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2602139, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.8, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.272858, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.858, "width_percent": 0.41}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.281815, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.268, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28293, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.319, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293904, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.377, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3020241, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.77, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.30305, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.822, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.315648, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.874, "width_percent": 0.386}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.32402, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.261, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3250048, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.302, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.341021, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.365, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.354466, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.766, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.355469, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.811, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372288, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.862, "width_percent": 0.413}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386477, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.275, "width_percent": 0.076}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3878229, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.351, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399773, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.408, "width_percent": 0.415}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408021, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.823, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4089541, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.867, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422111, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.918, "width_percent": 0.404}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43159, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.322, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432638, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.372, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.444376, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.426, "width_percent": 0.406}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.453089, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.831, "width_percent": 0.061}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.454205, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.893, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.465235, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.944, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4730659, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.344, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4740121, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.387, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4862082, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.44, "width_percent": 0.419}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.495682, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.86, "width_percent": 0.068}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4973469, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 26.927, "width_percent": 0.08}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5113409, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.008, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.52408, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.402, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.525104, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.456, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.540301, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.513, "width_percent": 0.402}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.553212, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.915, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.554169, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.961, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565528, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.014, "width_percent": 0.389}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.573523, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.402, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.574413, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.442, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5923529, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.5, "width_percent": 0.396}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.600957, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.896, "width_percent": 0.076}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.602349, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.972, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618067, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.03, "width_percent": 0.46}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.62671, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.49, "width_percent": 0.034}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627522, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.523, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639866, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.58, "width_percent": 0.366}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6484091, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.946, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649503, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.998, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66045, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.054, "width_percent": 0.386}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66861, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.44, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.669619, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.491, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680657, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.543, "width_percent": 0.395}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688013, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.938, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688893, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.976, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699616, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.032, "width_percent": 0.395}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706985, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.427, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708043, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.467, "width_percent": 0.07}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719695, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.537, "width_percent": 0.381}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727429, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.918, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728507, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.96, "width_percent": 0.06}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739358, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.02, "width_percent": 0.417}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74785, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.437, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749348, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.493, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7609758, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.552, "width_percent": 0.397}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7687268, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.949, "width_percent": 0.06}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769905, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.009, "width_percent": 0.076}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7827158, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.085, "width_percent": 0.492}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7911088, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.578, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.792006, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.618, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803601, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.669, "width_percent": 0.44}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.811909, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.109, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.813287, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.155, "width_percent": 0.079}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.823719, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.234, "width_percent": 0.403}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835671, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.637, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8365731, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.679, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8477218, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.728, "width_percent": 0.399}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8555489, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.128, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.856478, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.17, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8687801, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.22, "width_percent": 0.386}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.877095, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.606, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.878067, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.644, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.897038, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.7, "width_percent": 0.409}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.90948, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.109, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91052, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.163, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9263031, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.218, "width_percent": 0.404}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934309, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.623, "width_percent": 0.066}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935605, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.688, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945627, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.743, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956183, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.137, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957187, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.188, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9709058, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.245, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978808, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.663, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979947, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.716, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.990599, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.77, "width_percent": 0.38}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9983442, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.15, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.999378, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.2, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.0097861, "duration": 0.00749, "duration_str": "7.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.251, "width_percent": 0.793}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.021073, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.044, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.022051, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.092, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.03253, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.142, "width_percent": 0.403}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.040098, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.545, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.041027, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.588, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.051506, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.646, "width_percent": 0.437}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.059201, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.083, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.060118, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.126, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.071091, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.182, "width_percent": 0.409}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.078704, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.591, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.0800939, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.639, "width_percent": 0.101}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.090724, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.739, "width_percent": 0.41}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.102361, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.149, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.103426, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.199, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.11422, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.257, "width_percent": 0.417}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.121947, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.674, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.123022, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.73, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.1355278, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.789, "width_percent": 0.379}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.1433592, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.168, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.144394, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.217, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.1566439, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.277, "width_percent": 0.375}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.165091, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.651, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.166105, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.699, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.178297, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.753, "width_percent": 0.393}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.186764, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.146, "width_percent": 0.069}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.1880229, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.215, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.200176, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.274, "width_percent": 0.471}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.2090342, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.745, "width_percent": 0.048}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.210014, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.793, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.232399, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.849, "width_percent": 0.39}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.240527, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.239, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.2414231, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.279, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.254045, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.33, "width_percent": 0.407}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.262399, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.736, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.2634308, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.782, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.2758589, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.846, "width_percent": 0.416}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.284867, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.262, "width_percent": 0.077}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.286588, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.34, "width_percent": 0.088}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.3138258, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.428, "width_percent": 0.412}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.322141, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.839, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.323035, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.882, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.336003, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.934, "width_percent": 0.474}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.3450491, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.408, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.346075, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.458, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.357862, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.515, "width_percent": 0.406}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.371186, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.92, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.372271, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.978, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.388563, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.028, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.3968492, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.422, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.397946, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.475, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.409621, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.532, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.418843, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.926, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.4199982, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.978, "width_percent": 0.068}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.432647, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.046, "width_percent": 0.421}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.440855, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.467, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.4417229, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.506, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.453695, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.563, "width_percent": 0.401}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.4618802, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.964, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.4630299, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.011, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.47746, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.07, "width_percent": 0.391}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.49068, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.461, "width_percent": 0.053}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.491677, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.513, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.507683, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.572, "width_percent": 0.419}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.5207, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.991, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.521646, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.033, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.5333889, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.088, "width_percent": 0.395}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.541492, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.483, "width_percent": 0.042}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.5424092, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.526, "width_percent": 0.05}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.555098, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.575, "width_percent": 0.382}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.563419, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.958, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.564592, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.006, "width_percent": 0.084}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.5765579, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.09, "width_percent": 0.377}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.585422, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.467, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.586578, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.519, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.5984402, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.577, "width_percent": 0.421}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.606894, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.998, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.607789, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.039, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.619956, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.091, "width_percent": 0.411}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.628626, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.501, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.6299639, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.558, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.641692, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.617, "width_percent": 0.428}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.651135, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.045, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.652226, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.094, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.663419, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.157, "width_percent": 0.389}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.671824, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.545, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.67281, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.595, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.685664, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.648, "width_percent": 0.414}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.693875, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.062, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.694737, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.1, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.71082, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.151, "width_percent": 0.406}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.719474, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.557, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.720552, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.612, "width_percent": 0.078}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.7324228, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.69, "width_percent": 0.396}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.7407281, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.086, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.741683, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.133, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.7586222, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.185, "width_percent": 0.404}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.771253, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.59, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.77226, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.639, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.78465, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.693, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.792682, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.093, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.793624, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.138, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.806546, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.196, "width_percent": 0.403}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.819308, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.599, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.820404, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.653, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.831934, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.711, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.8439422, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.105, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.8448899, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.152, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.857538, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.21, "width_percent": 0.408}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.8700001, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.617, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.8709722, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.664, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.881606, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.716, "width_percent": 0.457}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.889921, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.173, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.8909252, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.224, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.9065058, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.275, "width_percent": 0.451}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.917589, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.726, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.918815, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.783, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.932697, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.847, "width_percent": 0.385}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.9406831, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.232, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.941652, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.279, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.954061, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.332, "width_percent": 0.41}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.962274, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.741, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.963334, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.795, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.976312, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.846, "width_percent": 0.408}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.985344, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.254, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.986565, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.309, "width_percent": 0.071}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834417.9987102, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.38, "width_percent": 0.4}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.0071042, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.78, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.008106, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.831, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.021056, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.887, "width_percent": 0.559}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.03112, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.446, "width_percent": 0.057}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.0322359, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.503, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.045368, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.562, "width_percent": 0.411}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.0582938, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.972, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.059216, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.018, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.071388, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.071, "width_percent": 0.373}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.07975, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.443, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.0808, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.482, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.09281, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.536, "width_percent": 0.376}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.1014268, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.911, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.102679, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.974, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.1156971, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.04, "width_percent": 0.388}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.126432, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.427, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.127361, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.473, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.1399019, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.529, "width_percent": 0.411}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.1487892, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.94, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.149829, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.989, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.162068, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.043, "width_percent": 0.393}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.170535, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.436, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.171911, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.509, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.1839852, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.566, "width_percent": 0.474}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.193122, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.041, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.19406, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.084, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.2067928, "duration": 0.00621, "duration_str": "6.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.135, "width_percent": 0.658}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.222301, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.792, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.223259, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.839, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.242748, "duration": 0.02256, "duration_str": "22.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.892, "width_percent": 2.389}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.27721, "duration": 0.03693, "duration_str": "36.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.281, "width_percent": 3.91}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.315252, "duration": 0.03337, "duration_str": "33.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.191, "width_percent": 3.533}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.376992, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.724, "width_percent": 0.364}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.39258, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.088, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.3937201, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.135, "width_percent": 0.049}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.4097412, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.183, "width_percent": 0.404}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.425604, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.588, "width_percent": 0.054}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.4270868, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.642, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.445161, "duration": 0.019059999999999997, "duration_str": "19.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.703, "width_percent": 2.018}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.4766738, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.721, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.4783542, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.784, "width_percent": 0.066}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.5118032, "duration": 0.00782, "duration_str": "7.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.85, "width_percent": 0.828}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.531952, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.677, "width_percent": 0.065}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.533983, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.742, "width_percent": 0.073}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.564725, "duration": 0.021920000000000002, "duration_str": "21.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.815, "width_percent": 2.321}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.598742, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.136, "width_percent": 0.075}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.60076, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.211, "width_percent": 0.09}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.6203, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.301, "width_percent": 0.391}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.6359532, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.692, "width_percent": 0.087}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.6379738, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.779, "width_percent": 0.101}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.656533, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.879, "width_percent": 0.368}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.664739, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.248, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.665827, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.286, "width_percent": 0.048}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.678708, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.334, "width_percent": 0.409}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.692712, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.742, "width_percent": 0.039}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.6936018, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.781, "width_percent": 0.043}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.706138, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.825, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.7145998, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.219, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.7156749, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.26, "width_percent": 0.062}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.728288, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.322, "width_percent": 0.377}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.736721, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.699, "width_percent": 0.055}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.737871, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.754, "width_percent": 0.057}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.749877, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.812, "width_percent": 0.411}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.758438, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.222, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.759446, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.273, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.774918, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.337, "width_percent": 0.39}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.788041, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.726, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.789234, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.778, "width_percent": 0.065}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.802183, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.843, "width_percent": 0.393}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.8104138, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.236, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.811378, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.28, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.827811, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.336, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.837429, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.77, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.838541, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.819, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.85088, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.875, "width_percent": 0.398}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.859192, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.273, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.860165, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.315, "width_percent": 0.049}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.87248, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.363, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.8807268, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.757, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.881841, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.802, "width_percent": 0.064}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.894212, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.865, "width_percent": 0.428}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.902916, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.293, "width_percent": 0.056}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.904072, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.349, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.917214, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.405, "width_percent": 0.392}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.9252439, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.797, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.926167, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.841, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.9417071, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.893, "width_percent": 0.42}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.950802, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.314, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.951957, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.36, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.964738, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.412, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.97297, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.83, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.973906, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.877, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.986184, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.928, "width_percent": 0.397}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.994363, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.325, "width_percent": 0.036}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834418.995229, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.361, "width_percent": 0.043}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.011561, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.404, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.0211, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.798, "width_percent": 0.049}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.022132, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.847, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.038276, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.9, "width_percent": 0.392}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.046882, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.291, "width_percent": 0.041}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.047982, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.333, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.059216, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.387, "width_percent": 0.394}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.0675502, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.781, "width_percent": 0.05}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.068664, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.83, "width_percent": 0.054}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.0839229, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.884, "width_percent": 0.397}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.092543, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.281, "width_percent": 0.04}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.093449, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.322, "width_percent": 0.052}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.109024, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.374, "width_percent": 0.374}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.1177928, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.747, "width_percent": 0.062}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.1191049, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.81, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.130819, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.868, "width_percent": 0.386}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.1389499, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.254, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.139892, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.299, "width_percent": 0.056}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.152199, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.355, "width_percent": 0.406}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.160821, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.761, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.161707, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.799, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `cover_image` = ?, `description` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.1728468, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.852, "width_percent": 0.418}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.180393, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.27, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.181386, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.315, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.1937108, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.377, "width_percent": 0.399}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.2022321, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.776, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.203357, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.828, "width_percent": 0.071}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.215744, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.899, "width_percent": 0.434}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.224037, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.333, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.224973, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.377, "width_percent": 0.051}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.241711, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.428, "width_percent": 0.416}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.2504551, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.844, "width_percent": 0.052}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.2516139, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.896, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.263047, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.958, "width_percent": 0.398}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.271306, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.356, "width_percent": 0.051}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.272301, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.406, "width_percent": 0.053}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.284993, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.459, "width_percent": 0.391}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.293051, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.85, "width_percent": 0.039}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.293931, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.889, "width_percent": 0.049}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.306005, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.938, "width_percent": 0.39}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.314139, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.328, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.315162, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.366, "width_percent": 0.061}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.327484, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.427, "width_percent": 0.397}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.340573, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.824, "width_percent": 0.047}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.341511, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.871, "width_percent": 0.058}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.3540778, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.929, "width_percent": 0.415}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.362454, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.344, "width_percent": 0.038}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.363339, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.382, "width_percent": 0.055}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.378796, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.437, "width_percent": 0.404}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.389334, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.842, "width_percent": 0.044}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.390269, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.886, "width_percent": 0.059}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.405624, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.945, "width_percent": 0.397}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.4141068, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.342, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.4152439, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.388, "width_percent": 0.077}, {"sql": "update `mp_stores` set `name` = ?, `logo` = ?, `logo_square` = ?, `description` = ?, `content` = ?, `status` = ?, `mp_stores`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.439424, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.465, "width_percent": 0.402}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.447691, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.868, "width_percent": 0.046}, {"sql": "select * from `mp_stores` where `email` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749834419.4491441, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.913, "width_percent": 0.087}, {"sql": "... 547 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Customer": {"value": 348, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 348, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 699, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/import/import", "action_name": "marketplace.store.import.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import", "uri": "POST admin/marketplaces/stores/import/import", "permission": "marketplace.store.import", "controller": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores/import", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:70-118</a>", "middleware": "web, core, auth", "duration": "10.08s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1706599584 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1706599584\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1493084686 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"84 characters\">brands_export_2025-06-13_20-43-01-e6d6fe421ca38f07a66ee633fd81cc65-684c5aadebeb0.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>total</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493084686\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1105834804 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IitEYmtjRUpINnh1ajkrWTlSSUE5Rmc9PSIsInZhbHVlIjoiWm9va29NTlVxRlRJWnFTaTh1d3kxc0YrREcrNmlaMXdTK2RXMEFpTUZIRzFFdEFEeVJLNHBnM1J0ZEw3KzF1VzhkRVgwVmJMN2t1eVpUWDFYakNuUkg3MnVZZXhTZzBVK29seXpsL1hzTGRJWndkejdmcmRWclRuMjVNVWtHdnUiLCJtYWMiOiIyZmUwYTczMjA3NDdhYWRlODBjNGRkN2FmNzIyMjYzYjQxMWRiZjYyYTkxN2ExMTA3NDM5MzcyMDRmODI4NjBjIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">741</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryhwGCwBHuCDESGbcW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">https://muhrak.gc/admin/marketplaces/stores/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitEYmtjRUpINnh1ajkrWTlSSUE5Rmc9PSIsInZhbHVlIjoiWm9va29NTlVxRlRJWnFTaTh1d3kxc0YrREcrNmlaMXdTK2RXMEFpTUZIRzFFdEFEeVJLNHBnM1J0ZEw3KzF1VzhkRVgwVmJMN2t1eVpUWDFYakNuUkg3MnVZZXhTZzBVK29seXpsL1hzTGRJWndkejdmcmRWclRuMjVNVWtHdnUiLCJtYWMiOiIyZmUwYTczMjA3NDdhYWRlODBjNGRkN2FmNzIyMjYzYjQxMWRiZjYyYTkxN2ExMTA3NDM5MzcyMDRmODI4NjBjIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InFGV0pvSERJR0hpTEJDcEhJbmxrSWc9PSIsInZhbHVlIjoiZSthc0xraEZiOHQwQkRHVE1KRUxyUkdCcVFhaWR4dVo4Mm03M0pCR1ZGUHNGYkc5U3dETGtTbEZvODAwM1ljaGQybys0Vy9FVHNYZTkvcFdYcUtubzUyekp2OXZXL3JuQ2pEWEZ4U2J6eldFaFJ3Z1RVUU9QQkV1eWQ3a0VQWG0iLCJtYWMiOiJjNDA0NjA0ZmQwOTJjZmM0Y2ZmMTAzOGFmMmU5MTA0YTA4OTY1NzQ3Y2M0MWYzZWJiNDQ2YzNjZTE4NGNkNTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105834804\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-967499186 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967499186\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-632671596 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:07:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632671596\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-147766839 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">https://muhrak.gc/admin/marketplaces/stores/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147766839\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/import/import", "action_name": "marketplace.store.import.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import"}, "badge": null}}