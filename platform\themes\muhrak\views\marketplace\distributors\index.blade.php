@php
    $itemsPerRow = 3;

@endphp

@if ($distributors->isNotEmpty())
<input type="hidden" id="found-listings" value="{{ $distributors instanceof \Illuminate\Pagination\LengthAwarePaginator ? $distributors->total() : $distributors->count() }}">
    @include(Theme::getThemeNamespace("views.marketplace.distributors.grid"), compact('itemsPerRow'))
@else
<div class="alert alert-warning" role="alert" id="no-distributors-found-filter-page-title">
    {{ __('No distributors found.') }}
</div>
@endif

@if ($distributors instanceof \Illuminate\Pagination\LengthAwarePaginator && $distributors->hasPages())
    <div class="justify-content-center wd-navigation mt15">
        {{ $distributors->withQueryString()->links(Theme::getThemeNamespace('partials.pagination')) }}
    </div>
@endif
