<?php

return [
    'name' => 'Stores',
    'create' => 'New store',
    'edit' => 'Edit store',
    'view' => 'View',
    'forms' => [
        'logo' => 'Logo',
        'email' => 'Email',
        'email_placeholder' => 'Ex: <EMAIL>',
        'phone' => 'Phone',
        'phone_placeholder' => 'Phone',
        'address' => 'Address',
        'address_placeholder' => 'Address',
        'city' => 'City',
        'city_placeholder' => 'City',
        'state' => 'State',
        'state_placeholder' => 'State',
        'country' => 'Country',
        'country_placeholder' => 'Country',
        'store_owner' => 'Store owner',
        'select_store_owner' => 'Select a store owner...',
        'store' => 'Store',
        'select_store' => 'Select a store...',
        'is_vendor' => 'Is vendor?',
        'company' => 'Company',
        'company_placeholder' => 'Company',
        'zip_code' => 'Zip Code',
        'zip_code_placeholder' => 'Zip Code',
        'tax_id' => 'Tax ID',
    ],
    'control' => [
        'title' => 'Vendor control',
        'block' => 'Block this vendor',
        'unblock' => 'Unblock this vendor',
        'block_confirmation' => 'Are you sure you really want to block this vendor?',
        'unblock_confirmation' => 'Are you sure you really want to unblock this vendor?',
        'block_reason' => 'Enter the reason why you are blocking this vendor',
        'blocked_success' => 'Blocked vendor successfully',
        'unblocked_success' => 'Unblocked vendor successfully',
        'block_help' => 'If a vendor is blocked, their products will no longer be visible on your site. You can unblock them at any time.',
        'blocked_help' => 'This vendor is blocked for the following reason: :reason. You can unblock them by clicking the button below.',
    ],
    'store' => 'Store',
    'stores' => 'Stores',
    'store_name' => 'Store name',
    'store_phone' => 'Store phone',
    'store_link' => 'Store link',
    'store_address' => 'Store address',
    'store_url' => 'Store URL',
    'product_approval_notification' => 'This product was created by :vendor. It needs to be approved to be shown on your website. :approve_link',
    'approve_here' => 'Approve it here.',
    'approve_product_confirmation' => 'Approve product confirmation',
    'approve_product_confirmation_description' => 'Are you sure you really want to approve this product from :vendor?',
    'approve' => 'Approve',
    'approve_product_success' => 'Approved product successfully',
    'product_approved_notification' => 'This product was created by :vendor. Approved by :user.',
    'information' => 'Store information',
    'vendor_information' => 'Vendor information',
    'vendor_name' => 'Name',
    'withdrawal_approval_notification' => 'This withdrawal request has been created by :vendor. Their current balance is :balance.',
    'edit_this_store' => 'Edit this store',
    'export' => [
        'total_stores' => 'Total stores',
        'description' => 'Export stores to CSV/Excel file',
    ],
    'import' => [
        'name' => 'Import stores',
        'description' => 'Import stores from CSV/Excel file',
    ],
];
