<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Bo<PERSON>ble\Marketplace\Http\Controllers\ExportStoreController;
use Botble\Marketplace\Http\Controllers\ImportStoreController;
use Bo<PERSON>ble\Marketplace\Http\Controllers\VendorBlockedController;
use Botble\Marketplace\Http\Controllers\WithdrawalInvoiceController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function (): void {
    Route::group(['namespace' => 'Botble\Marketplace\Http\Controllers'], function (): void {
        Route::group(['prefix' => 'marketplaces', 'as' => 'marketplace.'], function (): void {
            Route::group(['prefix' => 'stores', 'as' => 'store.'], function (): void {
                Route::resource('', 'StoreController')->parameters(['' => 'store']);
                Route::post('update-tax-info/{store}', [
                    'as' => 'update-tax-info',
                    'uses' => 'StoreController@updateTaxInformation',
                    'permission' => 'marketplace.store.edit',
                ]);
                Route::post('update-payout-info/{store}', [
                    'as' => 'update-payout-info',
                    'uses' => 'StoreController@updatePayoutInformation',
                    'permission' => 'marketplace.store.edit',
                ]);

                Route::get('view/{id}', [
                    'as' => 'view',
                    'uses' => 'StoreRevenueController@view',
                ])->wherePrimaryKey();

                Route::group(['prefix' => 'revenues', 'as' => 'revenue.'], function (): void {
                    Route::match(['GET', 'POST'], 'list/{id}', [
                        'as' => 'index',
                        'uses' => 'StoreRevenueController@index',
                        'permission' => 'marketplace.store.view',
                    ])->wherePrimaryKey();

                    Route::post('create/{id}', [
                        'as' => 'create',
                        'uses' => 'StoreRevenueController@store',
                    ])->wherePrimaryKey();
                });
            });

            // Store Import/Export Routes
            Route::group(['prefix' => 'stores/import', 'as' => 'store.import.', 'permission' => 'marketplace.store.import'], function (): void {
                Route::get('/', [ImportStoreController::class, 'index'])->name('index');
                Route::post('validate', [ImportStoreController::class, 'validateData'])->name('validate');
                Route::post('import', [ImportStoreController::class, 'import'])->name('store');
                Route::post('download-example', [ImportStoreController::class, 'downloadExample'])->name('download-example');
            });

            Route::group(['prefix' => 'stores/export', 'as' => 'store.export.', 'permission' => 'marketplace.store.export'], function (): void {
                Route::get('/', [ExportStoreController::class, 'index'])->name('index');
                Route::post('/', [ExportStoreController::class, 'store'])->name('store');
            });

            Route::group(['prefix' => 'withdrawals', 'as' => 'withdrawal.'], function (): void {
                Route::resource('', 'WithdrawalController')
                    ->parameters(['' => 'withdrawal'])
                    ->except([
                        'create',
                        'store',
                    ]);

                Route::get('{withdrawal}/invoice', [WithdrawalInvoiceController::class, '__invoke'])
                    ->name('invoice');
            });

            Route::get('settings', [
                'as' => 'settings',
                'uses' => 'Settings\MarketplaceSettingController@edit',
            ]);

            Route::put('settings', [
                'as' => 'settings.update',
                'uses' => 'Settings\MarketplaceSettingController@update',
                'permission' => 'marketplace.settings',
            ]);

            Route::group(['prefix' => 'unverified-vendors', 'as' => 'unverified-vendors.'], function (): void {
                Route::match(['GET', 'POST'], '/', [
                    'as' => 'index',
                    'uses' => 'UnverifiedVendorController@index',
                ]);

                Route::get('view/{id}', [
                    'as' => 'view',
                    'uses' => 'UnverifiedVendorController@view',
                    'permission' => 'marketplace.unverified-vendors.edit',
                ])->wherePrimaryKey();

                Route::post('approve/{id}', [
                    'as' => 'approve-vendor',
                    'uses' => 'UnverifiedVendorController@approveVendor',
                    'permission' => 'marketplace.unverified-vendors.edit',
                ])->wherePrimaryKey();

                Route::post('reject/{id}', [
                    'as' => 'reject-vendor',
                    'uses' => 'UnverifiedVendorController@rejectVendor',
                    'permission' => 'marketplace.unverified-vendors.edit',
                ])->wherePrimaryKey();

                Route::get('download-certificate/{id}', [
                    'as' => 'download-certificate',
                    'uses' => 'UnverifiedVendorController@downloadCertificate',
                    'permission' => 'marketplace.unverified-vendors.edit',
                ])->wherePrimaryKey();

                Route::get('download-government-id/{id}', [
                    'as' => 'download-government-id',
                    'uses' => 'UnverifiedVendorController@downloadGovernmentId',
                    'permission' => 'marketplace.unverified-vendors.edit',
                ])->wherePrimaryKey();
            });

            Route::group(['prefix' => 'vendors', 'as' => 'vendors.'], function (): void {
                Route::match(['GET', 'POST'], '/', [
                    'as' => 'index',
                    'uses' => 'VendorController@index',
                ]);

                Route::group(['permission' => 'marketplace.vendors.control'], function (): void {
                    Route::post('block/{id}', [VendorBlockedController::class, 'store'])->name('block');
                    Route::post('unblock/{id}', [VendorBlockedController::class, 'destroy'])->name('unblock');
                });
            });

            Route::group([
                'prefix' => 'reports',
                'as' => 'reports.',
                'permission' => 'marketplace.reports',
                'controller' => 'ReportController',
            ], function (): void {
                Route::get('', [
                    'as' => 'index',
                    'uses' => 'index',
                ]);

                Route::post('store-revenues', [
                    'as' => 'store-revenues',
                    'uses' => 'getStoreRevenues',
                ]);
            });

            Route::group(['prefix' => 'data-rooms', 'as' => 'data-room.'], function () {
                Route::resource('', 'DataRoomController')->except(['index'])->parameters(['' => 'data-room']);

                Route::match(['GET', 'POST'], 'list/{id}', [
                    'as' => 'index',
                    'uses' => 'DataRoomController@index',
                ])->wherePrimaryKey();

                Route::post('sorting', [
                    'as' => 'sorting',
                    'uses' => 'DataRoomController@postSorting',
                ]);

                Route::get('ajax-stores', [
                    'as' => 'ajax-stores',
                    'uses' => 'DataRoomController@getAjaxStores',
                ]);
            });

            Route::group(['prefix' => 'categories', 'as' => 'category.'], function () {
                Route::resource('', 'CategoryController')->parameters(['' => 'category']);

                Route::put('update-tree', [
                    'as' => 'update-tree',
                    'uses' => 'CategoryController@updateTree',
                ]);
            });

            Route::group(['prefix' => 'distributors', 'as' => 'distributor.'], function () {
                Route::resource('', 'DistributorController')->parameters(['' => 'distributor']);
            });

            Route::group(['prefix' => 'distributor-categories', 'as' => 'distributor-category.'], function () {
                Route::resource('', 'DistributorCategoryController')->parameters(['' => 'distributor-category']);
            });

            Route::get('get-list-distributors-for-search', [
                'as' => 'get-list-distributors-for-search',
                'uses' => 'StoreController@getListDistributorsForSearch',
            ]);

            Route::get('get-distributor-box/{id?}', [
                'as' => 'get-distributor-box',
                'uses' => 'StoreController@getDistributorBoxes',
            ]);


        });



        Route::group(['prefix' => 'ecommerce/products', 'as' => 'products.'], function (): void {
            Route::post('approve-product/{id}', [
                'as' => 'approve-product',
                'uses' => 'ProductController@approveProduct',
                'permission' => 'products.edit',
            ])->wherePrimaryKey();
        });
    });


});
