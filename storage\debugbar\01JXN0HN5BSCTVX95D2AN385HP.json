{"__meta": {"id": "01JXN0HN5BSCTVX95D2AN385HP", "datetime": "2025-06-13 16:04:13", "utime": **********.101339, "method": "POST", "uri": "/admin/tables/bulk-actions", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749830646.177743, "end": **********.101366, "duration": 6.923623085021973, "duration_str": "6.92s", "measures": [{"label": "Booting", "start": 1749830646.177743, "relative_start": 0, "end": **********.12619, "relative_end": **********.12619, "duration": 0.****************, "duration_str": "948ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.126203, "relative_start": 0.****************, "end": **********.10137, "relative_end": 4.0531158447265625e-06, "duration": 5.****************, "duration_str": "5.98s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.149048, "relative_start": 0.****************, "end": **********.161747, "relative_end": **********.161747, "duration": 0.012698888778686523, "duration_str": "12.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.273176, "relative_start": 1.***************, "end": **********.273176, "relative_end": **********.273176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.285077, "relative_start": 1.****************, "end": **********.285077, "relative_end": **********.285077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3952f2f8079cae92614235bd0a03d56", "start": **********.290528, "relative_start": 1.1127851009368896, "end": **********.290528, "relative_end": **********.290528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.291383, "relative_start": 1.1136400699615479, "end": **********.291383, "relative_end": **********.291383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.29234, "relative_start": 1.1145970821380615, "end": **********.29234, "relative_end": **********.29234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.292993, "relative_start": 1.1152501106262207, "end": **********.292993, "relative_end": **********.292993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.095309, "relative_start": 6.9175660610198975, "end": **********.0972, "relative_end": **********.0972, "duration": 0.0018908977508544922, "duration_str": "1.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 55526200, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 6, "nb_templates": 6, "templates": [{"name": "core/table::bulk-changes", "param_count": null, "params": [], "start": **********.273122, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.285034, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "__components::b3952f2f8079cae92614235bd0a03d56", "param_count": null, "params": [], "start": **********.290474, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b3952f2f8079cae92614235bd0a03d56.blade.php__components::b3952f2f8079cae92614235bd0a03d56", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb3952f2f8079cae92614235bd0a03d56.blade.php&line=1", "ajax": false, "filename": "b3952f2f8079cae92614235bd0a03d56.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.291254, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.292315, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.292955, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}]}, "queries": {"count": 500, "nb_statements": 2512, "nb_visible_statements": 500, "nb_excluded_statements": 2012, "nb_failed_statements": 0, "accumulated_duration": 0.8554500000000003, "accumulated_duration_str": "855ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.1788619, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.071}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "platform/core/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.1901722, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.071, "width_percent": 0.055}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.198935, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.126, "width_percent": 0.058}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (62, 61, 5, 4, 3, 2, 1) order by `mp_stores`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}, {"index": 19, "namespace": null, "name": "platform/core/table/src/Abstracts/Concerns/HasBulkActions.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\Concerns\\HasBulkActions.php", "line": 167}, {"index": 20, "namespace": null, "name": "platform/core/table/src/Http/Controllers/TableBulkActionController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Http\\Controllers\\TableBulkActionController.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.29445, "duration": 0.0058200000000000005, "duration_str": "5.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.185, "width_percent": 0.68}, {"sql": "delete from `mp_stores` where `id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, {"index": 18, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}, {"index": 19, "namespace": null, "name": "platform/core/table/src/Abstracts/Concerns/HasBulkActions.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\Concerns\\HasBulkActions.php", "line": 167}, {"index": 20, "namespace": null, "name": "platform/core/table/src/Http/Controllers/TableBulkActionController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Http\\Controllers\\TableBulkActionController.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.308104, "duration": 0.01635, "duration_str": "16.35ms", "memory": 0, "memory_str": null, "filename": "DeleteBulkAction.php:30", "source": {"index": 14, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fsrc%2FBulkActions%2FDeleteBulkAction.php&line=30", "ajax": false, "filename": "DeleteBulkAction.php", "line": "30"}, "connection": "muhrak", "explain": null, "start_percent": 0.865, "width_percent": 1.911}, {"sql": "select * from `slugs` where `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `reference_id` = 1 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, {"index": 28, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}, {"index": 29, "namespace": null, "name": "platform/core/table/src/Abstracts/Concerns/HasBulkActions.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\Concerns\\HasBulkActions.php", "line": 167}], "start": **********.326863, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.776, "width_percent": 0.507}, {"sql": "delete from `slugs` where `id` = 106", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, {"index": 28, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}, {"index": 29, "namespace": null, "name": "platform/core/table/src/Abstracts/Concerns/HasBulkActions.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\Concerns\\HasBulkActions.php", "line": 167}], "start": **********.332655, "duration": 0.00937, "duration_str": "9.37ms", "memory": 0, "memory_str": null, "filename": "SlugServiceProvider.php:145", "source": {"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FProviders%2FSlugServiceProvider.php&line=145", "ajax": false, "filename": "SlugServiceProvider.php", "line": "145"}, "connection": "muhrak", "explain": null, "start_percent": 3.284, "width_percent": 1.095}, {"sql": "delete from `slugs_translations` where `slugs_id` = 106", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 21, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 27, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, {"index": 31, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}], "start": **********.343396, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "LanguageAdvancedServiceProvider.php:85", "source": {"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FProviders%2FLanguageAdvancedServiceProvider.php&line=85", "ajax": false, "filename": "LanguageAdvancedServiceProvider.php", "line": "85"}, "connection": "muhrak", "explain": null, "start_percent": 4.379, "width_percent": 0.049}, {"sql": "select * from `ec_products` where `ec_products`.`store_id` = 1 and `ec_products`.`store_id` is not null order by `ec_products`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 27, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, {"index": 31, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}, {"index": 32, "namespace": null, "name": "platform/core/table/src/Abstracts/Concerns/HasBulkActions.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\Concerns\\HasBulkActions.php", "line": 167}], "start": **********.345932, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.428, "width_percent": 0.316}, {"sql": "delete from `ec_products` where `id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 27, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}, {"index": 31, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 29}, {"index": 32, "namespace": null, "name": "platform/core/table/src/Abstracts/Concerns/HasBulkActions.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\Concerns\\HasBulkActions.php", "line": 167}], "start": **********.350215, "duration": 0.011359999999999999, "duration_str": "11.36ms", "memory": 0, "memory_str": null, "filename": "Store.php:70", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=70", "ajax": false, "filename": "Store.php", "line": "70"}, "connection": "muhrak", "explain": null, "start_percent": 4.744, "width_percent": 1.328}, {"sql": "select * from `slugs` where `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product' and `reference_id` = 3 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 37, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}], "start": **********.363563, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.072, "width_percent": 0.464}, {"sql": "delete from `slugs` where `id` = 47", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 37, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}], "start": **********.3696191, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "SlugServiceProvider.php:145", "source": {"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FProviders%2FSlugServiceProvider.php&line=145", "ajax": false, "filename": "SlugServiceProvider.php", "line": "145"}, "connection": "muhrak", "explain": null, "start_percent": 6.536, "width_percent": 0.45}, {"sql": "delete from `slugs_translations` where `slugs_id` = 47", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 21, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.374597, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "LanguageAdvancedServiceProvider.php:85", "source": {"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FProviders%2FLanguageAdvancedServiceProvider.php&line=85", "ajax": false, "filename": "LanguageAdvancedServiceProvider.php", "line": "85"}, "connection": "muhrak", "explain": null, "start_percent": 6.986, "width_percent": 0.065}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 3 and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}], "start": **********.377846, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.051, "width_percent": 0.157}, {"sql": "delete from `ec_product_variations` where `id` = 5", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}], "start": **********.381221, "duration": 0.00563, "duration_str": "5.63ms", "memory": 0, "memory_str": null, "filename": "Product.php:121", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=121", "ajax": false, "filename": "Product.php", "line": "121"}, "connection": "muhrak", "explain": null, "start_percent": 7.208, "width_percent": 0.658}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = 5", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 30}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 38, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.456835, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:30", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=30", "ajax": false, "filename": "ProductVariation.php", "line": "30"}, "connection": "muhrak", "explain": null, "start_percent": 7.866, "width_percent": 0.421}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = 5 and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 31}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 41, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.4986691, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:31", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=31", "ajax": false, "filename": "ProductVariation.php", "line": "31"}, "connection": "muhrak", "explain": null, "start_percent": 8.287, "width_percent": 0.058}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 33}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.501694, "duration": 0.01159, "duration_str": "11.59ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 8.345, "width_percent": 1.355}, {"sql": "delete from `ec_products` where `id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.514446, "duration": 0.00707, "duration_str": "7.07ms", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:34", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=34", "ajax": false, "filename": "ProductVariation.php", "line": "34"}, "connection": "muhrak", "explain": null, "start_percent": 9.7, "width_percent": 0.826}, {"sql": "select * from `slugs` where `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product' and `reference_id` = 28 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product", 28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.522315, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.527, "width_percent": 0.076}, {"sql": "delete from `slugs` where `id` = 114", "type": "query", "params": [], "bindings": [114], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.523887, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "SlugServiceProvider.php:145", "source": {"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FProviders%2FSlugServiceProvider.php&line=145", "ajax": false, "filename": "SlugServiceProvider.php", "line": "145"}, "connection": "muhrak", "explain": null, "start_percent": 10.603, "width_percent": 0.634}, {"sql": "delete from `slugs_translations` where `slugs_id` = 114", "type": "query", "params": [], "bindings": [114], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 21, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.5299559, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "LanguageAdvancedServiceProvider.php:85", "source": {"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FProviders%2FLanguageAdvancedServiceProvider.php&line=85", "ajax": false, "filename": "LanguageAdvancedServiceProvider.php", "line": "85"}, "connection": "muhrak", "explain": null, "start_percent": 11.236, "width_percent": 0.049}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 28 and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.5309699, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.285, "width_percent": 0.213}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = 28 and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 122}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.535052, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:122", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=122", "ajax": false, "filename": "Product.php", "line": "122"}, "connection": "muhrak", "explain": null, "start_percent": 11.498, "width_percent": 0.054}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 123}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.536392, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:123", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=123", "ajax": false, "filename": "Product.php", "line": "123"}, "connection": "muhrak", "explain": null, "start_percent": 11.552, "width_percent": 0.069}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 124}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.537896, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Product.php:124", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=124", "ajax": false, "filename": "Product.php", "line": "124"}, "connection": "muhrak", "explain": null, "start_percent": 11.621, "width_percent": 0.048}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 125}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.539067, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:125", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=125", "ajax": false, "filename": "Product.php", "line": "125"}, "connection": "muhrak", "explain": null, "start_percent": 11.669, "width_percent": 0.057}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 126}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.540999, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Product.php:126", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=126", "ajax": false, "filename": "Product.php", "line": "126"}, "connection": "muhrak", "explain": null, "start_percent": 11.726, "width_percent": 0.044}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 127}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.542824, "duration": 0.02359, "duration_str": "23.59ms", "memory": 0, "memory_str": null, "filename": "Product.php:127", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=127", "ajax": false, "filename": "Product.php", "line": "127"}, "connection": "muhrak", "explain": null, "start_percent": 11.77, "width_percent": 2.758}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 128}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.5672379, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Product.php:128", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=128", "ajax": false, "filename": "Product.php", "line": "128"}, "connection": "muhrak", "explain": null, "start_percent": 14.528, "width_percent": 0.11}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 129}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.5694141, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Product.php:129", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=129", "ajax": false, "filename": "Product.php", "line": "129"}, "connection": "muhrak", "explain": null, "start_percent": 14.638, "width_percent": 0.096}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 28 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 429}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 232}], "start": **********.572242, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.734, "width_percent": 0.057}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 130}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.575563, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Product.php:130", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=130", "ajax": false, "filename": "Product.php", "line": "130"}, "connection": "muhrak", "explain": null, "start_percent": 14.791, "width_percent": 0.124}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = 28 and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 131}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.5966911, "duration": 0.008, "duration_str": "8ms", "memory": 0, "memory_str": null, "filename": "Product.php:131", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=131", "ajax": false, "filename": "Product.php", "line": "131"}, "connection": "muhrak", "explain": null, "start_percent": 14.915, "width_percent": 0.935}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = 28 and `ec_reviews`.`product_id` is not null and `status` = 'published'", "type": "query", "params": [], "bindings": [28, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 132}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.606961, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "Product.php:132", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=132", "ajax": false, "filename": "Product.php", "line": "132"}, "connection": "muhrak", "explain": null, "start_percent": 15.85, "width_percent": 0.341}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 133}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.611048, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:133", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=133", "ajax": false, "filename": "Product.php", "line": "133"}, "connection": "muhrak", "explain": null, "start_percent": 16.191, "width_percent": 0.055}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = 28 and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 134}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.6496818, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Product.php:134", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=134", "ajax": false, "filename": "Product.php", "line": "134"}, "connection": "muhrak", "explain": null, "start_percent": 16.246, "width_percent": 0.133}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 135}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.652309, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Product.php:135", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=135", "ajax": false, "filename": "Product.php", "line": "135"}, "connection": "muhrak", "explain": null, "start_percent": 16.38, "width_percent": 0.05}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 136}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.654161, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Product.php:136", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=136", "ajax": false, "filename": "Product.php", "line": "136"}, "connection": "muhrak", "explain": null, "start_percent": 16.43, "width_percent": 0.112}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 137}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.656476, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Product.php:137", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=137", "ajax": false, "filename": "Product.php", "line": "137"}, "connection": "muhrak", "explain": null, "start_percent": 16.542, "width_percent": 0.085}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = 28", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 138}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.658714, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Product.php:138", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=138", "ajax": false, "filename": "Product.php", "line": "138"}, "connection": "muhrak", "explain": null, "start_percent": 16.628, "width_percent": 0.039}, {"sql": "delete from `meta_boxes` where (`reference_id` = 28 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": [28, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.7008712, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:25", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FListeners%2FDeletedContentListener.php&line=25", "ajax": false, "filename": "DeletedContentListener.php", "line": "25"}, "connection": "muhrak", "explain": null, "start_percent": 16.666, "width_percent": 0.113}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 28 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": ["seo_meta", 28, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 189}, {"index": 16, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.839487, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 16.779, "width_percent": 0.098}, {"sql": "delete from `slugs` where (`reference_id` = 28 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": [28, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.866221, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:18", "source": {"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FDeletedContentListener.php&line=18", "ajax": false, "filename": "DeletedContentListener.php", "line": "18"}, "connection": "muhrak", "explain": null, "start_percent": 16.878, "width_percent": 0.068}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 963}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.883827, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.945, "width_percent": 0.13}, {"sql": "delete from `audit_histories` where `created_at` < '2025-05-14 16:04:07' limit 1000", "type": "query", "params": [], "bindings": ["2025-05-14 16:04:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.994892, "duration": 0.02534, "duration_str": "25.34ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:55", "source": {"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=55", "ajax": false, "filename": "AuditHandlerListener.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 17.075, "width_percent": 2.962}, {"sql": "delete from `audit_histories` where `created_at` < '2025-05-14 16:04:07' limit 1000", "type": "query", "params": [], "bindings": ["2025-05-14 16:04:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.036133, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:55", "source": {"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=55", "ajax": false, "filename": "AuditHandlerListener.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 20.037, "width_percent": 0.065}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'product', 'deleted', 1, 1, 28, 'Pulse Valve', 'danger', '2025-06-13 16:04:07', '2025-06-13 16:04:07', '{\\\"ids\\\":[\\\"62\\\",\\\"61\\\",\\\"5\\\",\\\"4\\\",\\\"3\\\",\\\"2\\\",\\\"1\\\"],\\\"bulk_action\\\":1,\\\"bulk_action_table\\\":\\\"Botble\\\\\\\\Marketplace\\\\\\\\Tables\\\\\\\\StoreTable\\\",\\\"bulk_action_target\\\":\\\"Botble\\\\\\\\Table\\\\\\\\BulkActions\\\\\\\\DeleteBulkAction\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "product", "deleted", 1, 1, 28, "Pulse Valve", "danger", "2025-06-13 16:04:07", "2025-06-13 16:04:07", "{\"ids\":[\"62\",\"61\",\"5\",\"4\",\"3\",\"2\",\"1\"],\"bulk_action\":1,\"bulk_action_table\":\"Botble\\\\Marketplace\\\\Tables\\\\StoreTable\",\"bulk_action_target\":\"Botble\\\\Table\\\\BulkActions\\\\DeleteBulkAction\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.0386262, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "muhrak", "explain": null, "start_percent": 20.103, "width_percent": 0.526}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'faq_schema_config' and `reference_id` = 28 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": ["faq_schema_config", 28, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/plugins/faq/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\faq\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 32, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.089308, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 20.629, "width_percent": 0.067}, {"sql": "delete from `ec_product_variations` where `id` = 6", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}], "start": **********.090725, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:121", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=121", "ajax": false, "filename": "Product.php", "line": "121"}, "connection": "muhrak", "explain": null, "start_percent": 20.696, "width_percent": 0.409}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = 6", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 30}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 38, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.0950851, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:30", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=30", "ajax": false, "filename": "ProductVariation.php", "line": "30"}, "connection": "muhrak", "explain": null, "start_percent": 21.105, "width_percent": 0.423}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = 6 and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 31}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 41, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.099246, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:31", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=31", "ajax": false, "filename": "ProductVariation.php", "line": "31"}, "connection": "muhrak", "explain": null, "start_percent": 21.528, "width_percent": 0.041}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 33}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.100703, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 21.569, "width_percent": 0.072}, {"sql": "delete from `ec_products` where `id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.102169, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:34", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=34", "ajax": false, "filename": "ProductVariation.php", "line": "34"}, "connection": "muhrak", "explain": null, "start_percent": 21.641, "width_percent": 0.468}, {"sql": "select * from `slugs` where `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product' and `reference_id` = 29 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product", 29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.1067228, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 22.109, "width_percent": 0.17}, {"sql": "delete from `slugs` where `id` = 115", "type": "query", "params": [], "bindings": [115], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.108958, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "SlugServiceProvider.php:145", "source": {"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FProviders%2FSlugServiceProvider.php&line=145", "ajax": false, "filename": "SlugServiceProvider.php", "line": "145"}, "connection": "muhrak", "explain": null, "start_percent": 22.278, "width_percent": 0.441}, {"sql": "delete from `slugs_translations` where `slugs_id` = 115", "type": "query", "params": [], "bindings": [115], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 21, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.113276, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "LanguageAdvancedServiceProvider.php:85", "source": {"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FProviders%2FLanguageAdvancedServiceProvider.php&line=85", "ajax": false, "filename": "LanguageAdvancedServiceProvider.php", "line": "85"}, "connection": "muhrak", "explain": null, "start_percent": 22.719, "width_percent": 0.05}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 29 and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.114259, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 22.769, "width_percent": 0.067}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = 29 and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 122}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1158278, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:122", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=122", "ajax": false, "filename": "Product.php", "line": "122"}, "connection": "muhrak", "explain": null, "start_percent": 22.836, "width_percent": 0.07}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 123}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1171188, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Product.php:123", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=123", "ajax": false, "filename": "Product.php", "line": "123"}, "connection": "muhrak", "explain": null, "start_percent": 22.906, "width_percent": 0.047}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 124}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.118125, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Product.php:124", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=124", "ajax": false, "filename": "Product.php", "line": "124"}, "connection": "muhrak", "explain": null, "start_percent": 22.953, "width_percent": 0.046}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 125}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.119211, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Product.php:125", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=125", "ajax": false, "filename": "Product.php", "line": "125"}, "connection": "muhrak", "explain": null, "start_percent": 22.998, "width_percent": 0.044}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 126}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.12108, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Product.php:126", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=126", "ajax": false, "filename": "Product.php", "line": "126"}, "connection": "muhrak", "explain": null, "start_percent": 23.043, "width_percent": 0.133}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 127}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1230772, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:127", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=127", "ajax": false, "filename": "Product.php", "line": "127"}, "connection": "muhrak", "explain": null, "start_percent": 23.176, "width_percent": 0.058}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 128}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.124285, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Product.php:128", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=128", "ajax": false, "filename": "Product.php", "line": "128"}, "connection": "muhrak", "explain": null, "start_percent": 23.235, "width_percent": 0.049}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 129}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.125429, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Product.php:129", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=129", "ajax": false, "filename": "Product.php", "line": "129"}, "connection": "muhrak", "explain": null, "start_percent": 23.284, "width_percent": 0.044}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 29 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 429}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 232}], "start": **********.126958, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 23.328, "width_percent": 0.069}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 130}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1305401, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Product.php:130", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=130", "ajax": false, "filename": "Product.php", "line": "130"}, "connection": "muhrak", "explain": null, "start_percent": 23.397, "width_percent": 0.072}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = 29 and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 131}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.132246, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Product.php:131", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=131", "ajax": false, "filename": "Product.php", "line": "131"}, "connection": "muhrak", "explain": null, "start_percent": 23.47, "width_percent": 0.049}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = 29 and `ec_reviews`.`product_id` is not null and `status` = 'published'", "type": "query", "params": [], "bindings": [29, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 132}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1339042, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Product.php:132", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 132}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=132", "ajax": false, "filename": "Product.php", "line": "132"}, "connection": "muhrak", "explain": null, "start_percent": 23.519, "width_percent": 0.072}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 133}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.135957, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:133", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=133", "ajax": false, "filename": "Product.php", "line": "133"}, "connection": "muhrak", "explain": null, "start_percent": 23.591, "width_percent": 0.058}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = 29 and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 134}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1371229, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Product.php:134", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=134", "ajax": false, "filename": "Product.php", "line": "134"}, "connection": "muhrak", "explain": null, "start_percent": 23.65, "width_percent": 0.068}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 135}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.138495, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Product.php:135", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=135", "ajax": false, "filename": "Product.php", "line": "135"}, "connection": "muhrak", "explain": null, "start_percent": 23.717, "width_percent": 0.047}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 136}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.139615, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Product.php:136", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=136", "ajax": false, "filename": "Product.php", "line": "136"}, "connection": "muhrak", "explain": null, "start_percent": 23.764, "width_percent": 0.046}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 137}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.140699, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Product.php:137", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=137", "ajax": false, "filename": "Product.php", "line": "137"}, "connection": "muhrak", "explain": null, "start_percent": 23.81, "width_percent": 0.044}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = 29", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 138}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.141985, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:138", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=138", "ajax": false, "filename": "Product.php", "line": "138"}, "connection": "muhrak", "explain": null, "start_percent": 23.854, "width_percent": 0.061}, {"sql": "delete from `meta_boxes` where (`reference_id` = 29 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": [29, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.144188, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:25", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Listeners\\DeletedContentListener.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FListeners%2FDeletedContentListener.php&line=25", "ajax": false, "filename": "DeletedContentListener.php", "line": "25"}, "connection": "muhrak", "explain": null, "start_percent": 23.915, "width_percent": 0.071}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 29 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": ["seo_meta", 29, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 189}, {"index": 16, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.1488328, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 23.986, "width_percent": 0.071}, {"sql": "delete from `slugs` where (`reference_id` = 29 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": [29, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.150918, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "DeletedContentListener.php:18", "source": {"index": 12, "namespace": null, "name": "platform/packages/slug/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\DeletedContentListener.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FDeletedContentListener.php&line=18", "ajax": false, "filename": "DeletedContentListener.php", "line": "18"}, "connection": "muhrak", "explain": null, "start_percent": 24.058, "width_percent": 0.064}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'product', 'deleted', 1, 1, 29, 'Pulse Valve', 'danger', '2025-06-13 16:04:08', '2025-06-13 16:04:08', '{\\\"ids\\\":[\\\"62\\\",\\\"61\\\",\\\"5\\\",\\\"4\\\",\\\"3\\\",\\\"2\\\",\\\"1\\\"],\\\"bulk_action\\\":1,\\\"bulk_action_table\\\":\\\"Botble\\\\\\\\Marketplace\\\\\\\\Tables\\\\\\\\StoreTable\\\",\\\"bulk_action_target\\\":\\\"Botble\\\\\\\\Table\\\\\\\\BulkActions\\\\\\\\DeleteBulkAction\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "product", "deleted", 1, 1, 29, "Pulse Valve", "danger", "2025-06-13 16:04:08", "2025-06-13 16:04:08", "{\"ids\":[\"62\",\"61\",\"5\",\"4\",\"3\",\"2\",\"1\"],\"bulk_action\":1,\"bulk_action_table\":\"Botble\\\\Marketplace\\\\Tables\\\\StoreTable\",\"bulk_action_target\":\"Botble\\\\Table\\\\BulkActions\\\\DeleteBulkAction\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.154357, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "muhrak", "explain": null, "start_percent": 24.122, "width_percent": 0.517}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'faq_schema_config' and `reference_id` = 29 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": ["faq_schema_config", 29, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/plugins/faq/src/Listeners/DeletedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\faq\\src\\Listeners\\DeletedContentListener.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 32, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.161202, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 24.638, "width_percent": 0.058}, {"sql": "delete from `ec_product_variations` where `id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/core/table/src/BulkActions/DeleteBulkAction.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\BulkActions\\DeleteBulkAction.php", "line": 30}], "start": **********.16216, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "Product.php:121", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=121", "ajax": false, "filename": "Product.php", "line": "121"}, "connection": "muhrak", "explain": null, "start_percent": 24.697, "width_percent": 0.418}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 30}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 38, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.166623, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:30", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=30", "ajax": false, "filename": "ProductVariation.php", "line": "30"}, "connection": "muhrak", "explain": null, "start_percent": 25.115, "width_percent": 0.448}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = 7 and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 31}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 41, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.171082, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:31", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=31", "ajax": false, "filename": "ProductVariation.php", "line": "31"}, "connection": "muhrak", "explain": null, "start_percent": 25.563, "width_percent": 0.056}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 33}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 36, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.1727421, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 25.619, "width_percent": 0.079}, {"sql": "delete from `ec_products` where `id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.174296, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "ProductVariation.php:34", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=34", "ajax": false, "filename": "ProductVariation.php", "line": "34"}, "connection": "muhrak", "explain": null, "start_percent": 25.699, "width_percent": 0.42}, {"sql": "select * from `slugs` where `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product' and `reference_id` = 30 order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product", 30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.178443, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 26.118, "width_percent": 0.17}, {"sql": "delete from `slugs` where `id` = 116", "type": "query", "params": [], "bindings": [116], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.180633, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "SlugServiceProvider.php:145", "source": {"index": 14, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FProviders%2FSlugServiceProvider.php&line=145", "ajax": false, "filename": "SlugServiceProvider.php", "line": "145"}, "connection": "muhrak", "explain": null, "start_percent": 26.288, "width_percent": 0.414}, {"sql": "delete from `slugs_translations` where `slugs_id` = 116", "type": "query", "params": [], "bindings": [116], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 21, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 145}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.184597, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "LanguageAdvancedServiceProvider.php:85", "source": {"index": 11, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/LanguageAdvancedServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\LanguageAdvancedServiceProvider.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FProviders%2FLanguageAdvancedServiceProvider.php&line=85", "ajax": false, "filename": "LanguageAdvancedServiceProvider.php", "line": "85"}, "connection": "muhrak", "explain": null, "start_percent": 26.702, "width_percent": 0.043}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 30 and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}], "start": **********.1854842, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 26.745, "width_percent": 0.051}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = 30 and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 122}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 34, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 40, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1866539, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Product.php:122", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=122", "ajax": false, "filename": "Product.php", "line": "122"}, "connection": "muhrak", "explain": null, "start_percent": 26.796, "width_percent": 0.044}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 123}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.187641, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Product.php:123", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=123", "ajax": false, "filename": "Product.php", "line": "123"}, "connection": "muhrak", "explain": null, "start_percent": 26.841, "width_percent": 0.043}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 124}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1885989, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Product.php:124", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=124", "ajax": false, "filename": "Product.php", "line": "124"}, "connection": "muhrak", "explain": null, "start_percent": 26.884, "width_percent": 0.042}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 125}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.189531, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Product.php:125", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=125", "ajax": false, "filename": "Product.php", "line": "125"}, "connection": "muhrak", "explain": null, "start_percent": 26.926, "width_percent": 0.042}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 126}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.190461, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Product.php:126", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=126", "ajax": false, "filename": "Product.php", "line": "126"}, "connection": "muhrak", "explain": null, "start_percent": 26.968, "width_percent": 0.039}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 127}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.191499, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Product.php:127", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=127", "ajax": false, "filename": "Product.php", "line": "127"}, "connection": "muhrak", "explain": null, "start_percent": 27.007, "width_percent": 0.046}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 128}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1924992, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Product.php:128", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=128", "ajax": false, "filename": "Product.php", "line": "128"}, "connection": "muhrak", "explain": null, "start_percent": 27.052, "width_percent": 0.037}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 129}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.193399, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Product.php:129", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=129", "ajax": false, "filename": "Product.php", "line": "129"}, "connection": "muhrak", "explain": null, "start_percent": 27.09, "width_percent": 0.041}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 30 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 429}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 232}], "start": **********.1943681, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 27.131, "width_percent": 0.051}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = 30", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 130}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductVariation.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\ProductVariation.php", "line": 34}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 121}, {"index": 37, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 70}], "start": **********.1962519, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:130", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=130", "ajax": false, "filename": "Product.php", "line": "130"}, "connection": "muhrak", "explain": null, "start_percent": 27.182, "width_percent": 0.065}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.197352, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.248, "width_percent": 0.043}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.197975, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.291, "width_percent": 0.048}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1988091, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.339, "width_percent": 0.039}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.199393, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.377, "width_percent": 0.041}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.200142, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.418, "width_percent": 0.041}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.200993, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.459, "width_percent": 0.055}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.20194, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.514, "width_percent": 0.039}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2026482, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.553, "width_percent": 0.035}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.203435, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.588, "width_percent": 0.041}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.205264, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.629, "width_percent": 0.04}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.205949, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.668, "width_percent": 0.039}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.208575, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.707, "width_percent": 0.478}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.214738, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.185, "width_percent": 0.069}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.215594, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.254, "width_percent": 0.449}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.220415, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.703, "width_percent": 0.447}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.224592, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.15, "width_percent": 0.055}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.226215, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.205, "width_percent": 0.089}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.227725, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.293, "width_percent": 0.505}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.23243, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.798, "width_percent": 0.074}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.233815, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.872, "width_percent": 0.471}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2381341, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.343, "width_percent": 0.055}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.238971, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.398, "width_percent": 0.065}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.240176, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.463, "width_percent": 0.049}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.241096, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.513, "width_percent": 0.051}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.242358, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.564, "width_percent": 0.047}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.243191, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.611, "width_percent": 0.046}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.244351, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.656, "width_percent": 0.047}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.245277, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.703, "width_percent": 0.049}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.246536, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.752, "width_percent": 0.044}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.247312, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.797, "width_percent": 0.037}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.24809, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.834, "width_percent": 0.055}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.249595, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.889, "width_percent": 0.039}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.250191, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.928, "width_percent": 0.048}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.250848, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.976, "width_percent": 0.039}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.251581, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.014, "width_percent": 0.046}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.252255, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.06, "width_percent": 0.046}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.253441, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.105, "width_percent": 0.042}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2541962, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.147, "width_percent": 0.035}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.254911, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.182, "width_percent": 0.04}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.255641, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.222, "width_percent": 0.035}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.256449, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.257, "width_percent": 0.042}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.259934, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.299, "width_percent": 0.043}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2606618, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.343, "width_percent": 0.047}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2629468, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.389, "width_percent": 0.436}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.268081, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.825, "width_percent": 0.048}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.26877, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.873, "width_percent": 0.042}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.269596, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.915, "width_percent": 0.555}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2749312, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.471, "width_percent": 0.463}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279528, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.934, "width_percent": 0.538}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2846541, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.471, "width_percent": 0.051}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2855952, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.523, "width_percent": 0.649}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.29177, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.171, "width_percent": 0.092}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293349, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.264, "width_percent": 0.078}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2946668, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.342, "width_percent": 0.544}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2997901, "duration": 0.00925, "duration_str": "9.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.886, "width_percent": 1.081}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.309656, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.967, "width_percent": 0.498}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.314708, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.465, "width_percent": 0.646}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.32059, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.111, "width_percent": 0.055}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.321561, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.166, "width_percent": 0.545}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3269432, "duration": 0.00467, "duration_str": "4.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.711, "width_percent": 0.546}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.332166, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.257, "width_percent": 0.457}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3365161, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.714, "width_percent": 0.051}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.337148, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.766, "width_percent": 0.411}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3409789, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.177, "width_percent": 0.065}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.342086, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.243, "width_percent": 0.433}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.346061, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.675, "width_percent": 0.044}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.346771, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.719, "width_percent": 0.146}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3485749, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.866, "width_percent": 0.411}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.352721, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.277, "width_percent": 0.477}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.357268, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.754, "width_percent": 0.079}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.359076, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.833, "width_percent": 0.123}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.360817, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.956, "width_percent": 0.452}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.365105, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.409, "width_percent": 0.09}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.36649, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.499, "width_percent": 0.44}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3706691, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.938, "width_percent": 0.057}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3715398, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.995, "width_percent": 0.05}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372572, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.046, "width_percent": 0.04}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.373383, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.085, "width_percent": 0.043}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3745651, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.129, "width_percent": 0.036}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.375299, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.165, "width_percent": 0.042}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3765202, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.207, "width_percent": 0.055}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.377511, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.262, "width_percent": 0.043}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.378339, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.305, "width_percent": 0.046}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.379199, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.351, "width_percent": 0.041}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3800201, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.392, "width_percent": 0.056}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381648, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.448, "width_percent": 0.044}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.382311, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.492, "width_percent": 0.036}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.382939, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.528, "width_percent": 0.051}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3843431, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.58, "width_percent": 0.053}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.385323, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.633, "width_percent": 0.046}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38644, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.678, "width_percent": 0.064}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388124, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.742, "width_percent": 0.105}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389912, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.848, "width_percent": 0.062}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391483, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.91, "width_percent": 0.053}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3927019, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.962, "width_percent": 0.065}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396453, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.028, "width_percent": 0.069}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397807, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.097, "width_percent": 0.048}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400219, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.145, "width_percent": 0.458}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407154, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.603, "width_percent": 0.061}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407903, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.664, "width_percent": 0.41}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411949, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.074, "width_percent": 0.471}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4163132, "duration": 0.00643, "duration_str": "6.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 44.545, "width_percent": 0.752}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4237309, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.297, "width_percent": 0.087}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4250681, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.383, "width_percent": 0.429}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429025, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.812, "width_percent": 0.07}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4301488, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.882, "width_percent": 0.435}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4341662, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.317, "width_percent": 0.051}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434956, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.369, "width_percent": 0.074}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.436223, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.442, "width_percent": 0.065}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437336, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.508, "width_percent": 0.067}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4383461, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.574, "width_percent": 0.054}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4391918, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.628, "width_percent": 0.048}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.440001, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.676, "width_percent": 0.05}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4408412, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.726, "width_percent": 0.053}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4417038, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.779, "width_percent": 0.05}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442797, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.829, "width_percent": 0.04}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443676, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.869, "width_percent": 0.155}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.446212, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.024, "width_percent": 0.056}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4470181, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.08, "width_percent": 0.053}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447774, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.133, "width_percent": 0.048}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.448713, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.181, "width_percent": 0.051}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.449543, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.232, "width_percent": 0.05}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.450471, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.283, "width_percent": 0.041}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451398, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.324, "width_percent": 0.047}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.452823, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.37, "width_percent": 0.058}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.453816, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.429, "width_percent": 0.036}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.454676, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.465, "width_percent": 0.047}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4567091, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.512, "width_percent": 0.055}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4576528, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.567, "width_percent": 0.048}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460669, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.615, "width_percent": 0.454}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.466191, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.068, "width_percent": 0.088}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467339, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.156, "width_percent": 0.463}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4718919, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.619, "width_percent": 0.44}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.476042, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.058, "width_percent": 0.063}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.478623, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.122, "width_percent": 0.081}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.479971, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.202, "width_percent": 0.472}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.484412, "duration": 0.007030000000000001, "duration_str": "7.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.674, "width_percent": 0.822}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4921088, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.496, "width_percent": 0.437}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.496145, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.933, "width_percent": 0.047}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4968572, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.98, "width_percent": 0.051}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.497865, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.032, "width_percent": 0.042}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.498637, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.074, "width_percent": 0.042}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.499402, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.116, "width_percent": 0.042}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500146, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.158, "width_percent": 0.046}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500922, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.203, "width_percent": 0.042}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.501667, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.246, "width_percent": 0.046}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.502439, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.291, "width_percent": 0.039}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.503146, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.33, "width_percent": 0.04}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.503913, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.369, "width_percent": 0.047}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.505294, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.416, "width_percent": 0.04}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.505889, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.456, "width_percent": 0.042}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.506506, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.498, "width_percent": 0.043}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.507266, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.541, "width_percent": 0.036}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.507828, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.578, "width_percent": 0.037}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.508521, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.615, "width_percent": 0.036}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.509338, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.651, "width_percent": 0.04}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.510277, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.691, "width_percent": 0.062}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5113, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.753, "width_percent": 0.04}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.512145, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.793, "width_percent": 0.041}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.514006, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.834, "width_percent": 0.043}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.514773, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.877, "width_percent": 0.046}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.517282, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.922, "width_percent": 0.472}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.523021, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.395, "width_percent": 0.067}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.523819, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.461, "width_percent": 0.452}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.528699, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.914, "width_percent": 0.451}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.533159, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.365, "width_percent": 0.053}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.534673, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.417, "width_percent": 0.09}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5366452, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.508, "width_percent": 0.444}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.540861, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.952, "width_percent": 0.071}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.542057, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.023, "width_percent": 0.472}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.54639, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.495, "width_percent": 0.051}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.547174, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.547, "width_percent": 0.058}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.548797, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.605, "width_percent": 0.049}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.549654, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.654, "width_percent": 0.047}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.550466, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.701, "width_percent": 0.044}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5512369, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.745, "width_percent": 0.046}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.552018, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.791, "width_percent": 0.044}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.552789, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.835, "width_percent": 0.047}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.553567, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.882, "width_percent": 0.046}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.554337, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.928, "width_percent": 0.044}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.555167, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.972, "width_percent": 0.056}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.556679, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.028, "width_percent": 0.048}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.557364, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.076, "width_percent": 0.047}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5580199, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.123, "width_percent": 0.049}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.558985, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.172, "width_percent": 0.039}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5596569, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.211, "width_percent": 0.048}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.560519, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.259, "width_percent": 0.034}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.561246, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.293, "width_percent": 0.039}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.561964, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.331, "width_percent": 0.035}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.562693, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.366, "width_percent": 0.036}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563508, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.402, "width_percent": 0.037}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565486, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.44, "width_percent": 0.062}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.56653, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.502, "width_percent": 0.05}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5689862, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.552, "width_percent": 0.424}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5740461, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.976, "width_percent": 0.054}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5749, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.03, "width_percent": 0.047}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.575732, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.077, "width_percent": 0.415}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.579688, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.492, "width_percent": 0.41}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.583656, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.902, "width_percent": 0.443}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.587976, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.345, "width_percent": 0.054}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5889912, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.399, "width_percent": 0.463}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.593483, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.862, "width_percent": 0.06}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.594395, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.922, "width_percent": 0.046}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.595272, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.967, "width_percent": 0.448}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5995529, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.415, "width_percent": 0.539}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604708, "duration": 0.0248, "duration_str": "24.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.954, "width_percent": 2.899}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630575, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.853, "width_percent": 0.435}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.634978, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.288, "width_percent": 0.055}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.636485, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.343, "width_percent": 0.51}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.641963, "duration": 0.01132, "duration_str": "11.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.852, "width_percent": 1.323}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654309, "duration": 0.00873, "duration_str": "8.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.176, "width_percent": 1.021}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.664173, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.196, "width_percent": 0.082}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665154, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.278, "width_percent": 0.482}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6696951, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.76, "width_percent": 0.077}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.670946, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.837, "width_percent": 0.459}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675176, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.296, "width_percent": 0.049}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676285, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.345, "width_percent": 0.131}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677996, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.476, "width_percent": 0.454}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682495, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.93, "width_percent": 0.449}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686691, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.379, "width_percent": 0.055}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68823, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.434, "width_percent": 0.076}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6896958, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.509, "width_percent": 0.478}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.694255, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.988, "width_percent": 0.074}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6955159, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.061, "width_percent": 0.454}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6997418, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.515, "width_percent": 0.071}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7009711, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.586, "width_percent": 0.081}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7023401, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.667, "width_percent": 0.053}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.70327, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.719, "width_percent": 0.061}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704277, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.78, "width_percent": 0.044}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705053, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.825, "width_percent": 0.041}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705801, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.866, "width_percent": 0.041}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706554, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.906, "width_percent": 0.043}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707318, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.95, "width_percent": 0.044}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708102, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.994, "width_percent": 0.044}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708956, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.039, "width_percent": 0.146}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711302, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.185, "width_percent": 0.041}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7119179, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.226, "width_percent": 0.046}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712583, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.271, "width_percent": 0.044}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.713477, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.316, "width_percent": 0.04}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714132, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.355, "width_percent": 0.042}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714935, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.397, "width_percent": 0.039}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.715663, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.436, "width_percent": 0.043}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.716448, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.479, "width_percent": 0.043}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717219, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.522, "width_percent": 0.036}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718035, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.559, "width_percent": 0.042}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719898, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.601, "width_percent": 0.047}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.72068, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.648, "width_percent": 0.043}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722889, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.691, "width_percent": 0.418}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727866, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.109, "width_percent": 0.047}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728447, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.156, "width_percent": 0.392}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732276, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.548, "width_percent": 0.417}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7361288, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.965, "width_percent": 0.049}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7375062, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.014, "width_percent": 0.077}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738793, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.091, "width_percent": 0.475}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743202, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.566, "width_percent": 0.081}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74454, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.647, "width_percent": 0.462}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748784, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.108, "width_percent": 0.053}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7495599, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.161, "width_percent": 0.063}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7506628, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.224, "width_percent": 0.049}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751513, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.273, "width_percent": 0.054}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7524672, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.327, "width_percent": 0.056}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7534091, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.383, "width_percent": 0.054}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.754623, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.437, "width_percent": 0.043}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7554781, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.48, "width_percent": 0.051}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7563438, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.531, "width_percent": 0.047}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757182, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.578, "width_percent": 0.047}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758163, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.625, "width_percent": 0.05}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760635, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.675, "width_percent": 0.062}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.761508, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.737, "width_percent": 0.044}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762176, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.782, "width_percent": 0.041}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763002, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.822, "width_percent": 0.042}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763664, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.865, "width_percent": 0.04}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764461, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.904, "width_percent": 0.043}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765354, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.948, "width_percent": 0.049}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766388, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.997, "width_percent": 0.062}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7675278, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.059, "width_percent": 0.048}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768574, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.107, "width_percent": 0.053}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7706242, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.159, "width_percent": 0.056}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771507, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.215, "width_percent": 0.048}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7739959, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.263, "width_percent": 0.452}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.779604, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.716, "width_percent": 0.067}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780484, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.782, "width_percent": 0.044}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.781399, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.827, "width_percent": 0.448}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.785876, "duration": 0.00915, "duration_str": "9.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.274, "width_percent": 1.07}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.796063, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.344, "width_percent": 0.428}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.800226, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.772, "width_percent": 0.048}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801107, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.82, "width_percent": 0.431}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.805711, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.251, "width_percent": 0.684}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8120759, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.935, "width_percent": 0.05}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.812998, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.985, "width_percent": 0.414}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8169072, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.399, "width_percent": 0.484}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.821617, "duration": 0.00477, "duration_str": "4.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.883, "width_percent": 0.558}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.827218, "duration": 0.00945, "duration_str": "9.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.441, "width_percent": 1.105}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.837136, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.545, "width_percent": 0.058}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838158, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.604, "width_percent": 0.042}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839493, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.646, "width_percent": 0.47}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.844598, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.116, "width_percent": 0.445}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849487, "duration": 0.00574, "duration_str": "5.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.561, "width_percent": 0.671}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855628, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.232, "width_percent": 0.464}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.860146, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.696, "width_percent": 0.098}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.861611, "duration": 0.00567, "duration_str": "5.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.794, "width_percent": 0.663}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.867565, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.457, "width_percent": 0.05}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.868313, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.507, "width_percent": 0.056}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.869361, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.564, "width_percent": 0.42}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.873518, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.983, "width_percent": 0.427}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.877505, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.41, "width_percent": 0.054}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.878953, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.464, "width_percent": 0.068}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8801188, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.531, "width_percent": 0.395}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8837721, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.927, "width_percent": 0.058}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.88474, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.985, "width_percent": 0.409}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.888485, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.394, "width_percent": 0.044}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889161, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.439, "width_percent": 0.056}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.890193, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.495, "width_percent": 0.046}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.891061, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.54, "width_percent": 0.044}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8918478, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.585, "width_percent": 0.044}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8926299, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.629, "width_percent": 0.043}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.893387, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.672, "width_percent": 0.044}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.89417, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.717, "width_percent": 0.042}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.894901, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.759, "width_percent": 0.043}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.895655, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.802, "width_percent": 0.042}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.896466, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.844, "width_percent": 0.049}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8979168, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.893, "width_percent": 0.049}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898719, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.942, "width_percent": 0.055}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8994799, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.997, "width_percent": 0.044}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.900307, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.042, "width_percent": 0.04}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.900923, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.082, "width_percent": 0.037}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9016402, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.119, "width_percent": 0.035}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9023461, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.154, "width_percent": 0.039}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903174, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.193, "width_percent": 0.049}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9040902, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.242, "width_percent": 0.033}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.904877, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.274, "width_percent": 0.042}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.906961, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.317, "width_percent": 0.044}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.907699, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.361, "width_percent": 0.037}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.910087, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.398, "width_percent": 0.449}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.915679, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.847, "width_percent": 0.077}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.916675, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.924, "width_percent": 0.424}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.920885, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.349, "width_percent": 0.444}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9250422, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.793, "width_percent": 0.058}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9267, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.851, "width_percent": 0.144}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.92868, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.995, "width_percent": 0.441}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932987, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.436, "width_percent": 0.078}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934384, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.514, "width_percent": 0.452}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93857, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.967, "width_percent": 0.057}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.939458, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.024, "width_percent": 0.067}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.941176, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.09, "width_percent": 0.048}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942018, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.138, "width_percent": 0.047}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942838, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.185, "width_percent": 0.039}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943587, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.224, "width_percent": 0.046}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.944774, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.269, "width_percent": 0.042}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945589, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.311, "width_percent": 0.044}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946673, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.356, "width_percent": 0.047}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9474661, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.403, "width_percent": 0.051}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9483862, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.454, "width_percent": 0.053}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950962, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.507, "width_percent": 0.044}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951634, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.551, "width_percent": 0.044}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952462, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.595, "width_percent": 0.042}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953226, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.638, "width_percent": 0.037}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953811, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.675, "width_percent": 0.047}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9545982, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.722, "width_percent": 0.035}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9552898, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.757, "width_percent": 0.047}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956073, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.804, "width_percent": 0.035}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956765, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.839, "width_percent": 0.046}, {"sql": "delete from `meta_boxes` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957686, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.884, "width_percent": 0.043}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961181, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.927, "width_percent": 0.043}, {"sql": "delete from `slugs` where (`reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961915, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.971, "width_percent": 0.039}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9643998, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.009, "width_percent": 0.459}, {"sql": "delete from `meta_boxes` where (`meta_key` = ? and `reference_id` = ? and `reference_type` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.971469, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.469, "width_percent": 0.06}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9722838, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.528, "width_percent": 0.046}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973144, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.574, "width_percent": 0.428}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977431, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.002, "width_percent": 0.429}, {"sql": "delete from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981749, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.431, "width_percent": 0.434}, {"sql": "delete from `ec_discount_products` where `ec_discount_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986451, "duration": 0.006, "duration_str": "6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.864, "width_percent": 0.701}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.993388, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.566, "width_percent": 0.494}, {"sql": "delete from `ec_product_up_sale_relations` where `ec_product_up_sale_relations`.`from_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9983048, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.06, "width_percent": 0.049}, {"sql": "delete from `ec_grouped_products` where `ec_grouped_products`.`parent_product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9992101, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.109, "width_percent": 0.054}, {"sql": "delete from `ec_tax_products` where `ec_tax_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.000353, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.163, "width_percent": 0.452}, {"sql": "delete from `ec_product_views` where `ec_product_views`.`product_id` = ? and `ec_product_views`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.004656, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.616, "width_percent": 0.475}, {"sql": "delete from `ec_reviews` where `ec_reviews`.`product_id` = ? and `ec_reviews`.`product_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.009233, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.09, "width_percent": 0.482}, {"sql": "delete from `ec_flash_sale_products` where `ec_flash_sale_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.013933, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.572, "width_percent": 0.454}, {"sql": "delete from `ec_product_files` where `ec_product_files`.`product_id` = ? and `ec_product_files`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.018348, "duration": 0.00638, "duration_str": "6.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.025, "width_percent": 0.746}, {"sql": "delete from `ec_product_label_products` where `ec_product_label_products`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.025229, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.771, "width_percent": 0.058}, {"sql": "delete from `ec_product_tag_product` where `ec_product_tag_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.026365, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.83, "width_percent": 0.431}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.030529, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.261, "width_percent": 0.423}, {"sql": "delete from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.034549, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.684, "width_percent": 0.048}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.0351489, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.732, "width_percent": 0.396}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.038863, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.128, "width_percent": 0.065}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.039967, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.194, "width_percent": 0.483}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.044386, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.677, "width_percent": 0.048}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.045202, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.725, "width_percent": 0.324}, {"sql": "delete from `ec_product_variations` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.048682, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.048, "width_percent": 0.464}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.053276, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.512, "width_percent": 0.469}, {"sql": "delete from `ec_product_variation_items` where `ec_product_variation_items`.`variation_id` = ? and `ec_product_variation_items`.`variation_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.0578132, "duration": 0.00643, "duration_str": "6.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.981, "width_percent": 0.752}, {"sql": "select * from `ec_products` where `ec_products`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.065522, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.733, "width_percent": 0.084}, {"sql": "delete from `ec_products` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.067036, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.817, "width_percent": 0.415}, {"sql": "select * from `slugs` where `reference_type` = ? and `reference_id` = ? order by `slugs`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.0712411, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.232, "width_percent": 0.075}, {"sql": "delete from `slugs` where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.072556, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.307, "width_percent": 0.479}, {"sql": "delete from `slugs_translations` where `slugs_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.0770772, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.786, "width_percent": 0.056}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null order by `ec_product_variations`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.077893, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.842, "width_percent": 0.058}, {"sql": "delete from `ec_product_variations` where `ec_product_variations`.`product_id` = ? and `ec_product_variations`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.0790172, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.901, "width_percent": 0.051}, {"sql": "delete from `ec_product_category_product` where `ec_product_category_product`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749830649.079921, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.952, "width_percent": 0.048}, {"sql": "... 2012 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Slug\\Models\\Slug": {"value": 89, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"value": 83, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"value": 59, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\ACL\\Models\\UserMeta": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUserMeta.php&line=1", "ajax": false, "filename": "UserMeta.php", "line": "?"}}}, "count": 243, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tables/bulk-actions", "action_name": "table.bulk-action.dispatch", "controller_action": "Botble\\Table\\Http\\Controllers\\TableBulkActionController@__invoke", "uri": "POST admin/tables/bulk-actions", "controller": "Botble\\Table\\Http\\Controllers\\TableBulkActionController@__invoke<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fsrc%2FHttp%2FControllers%2FTableBulkActionController.php&line=12\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/tables/bulk-actions", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fsrc%2FHttp%2FControllers%2FTableBulkActionController.php&line=12\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/core/table/src/Http/Controllers/TableBulkActionController.php:12-39</a>", "middleware": "web, core, auth", "duration": "6.95s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-139267108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-139267108\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-99544952 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ids</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">61</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>5</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>4</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str>2</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>bulk_action</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>bulk_action_table</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Botble\\Marketplace\\Tables\\StoreTable</span>\"\n  \"<span class=sf-dump-key>bulk_action_target</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Botble\\Table\\BulkActions\\DeleteBulkAction</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99544952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1695795687 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlBaWndvZ0hBejluTy9FdzA5bG54dHc9PSIsInZhbHVlIjoiU2pGUmtieFo0OGs1bzBWeGxnUWZyNDR0dUdOTjlUY1NBdVZvOGFBaUVxZ1BSRzIza2phbTdMT2I3UlVXaDJ1NmZ4bEdrc3BRVlFwSGVsVSt3WFlOTFJPbnErTEFRekJZVTkxUmxXV3JUcTA0S0w5UHEyV3pvRkFmNWpWOWVtcFoiLCJtYWMiOiI3MWQxNTYwNGYxZjU4MTIyYmMyYjk3YWNjYjkyMjFlMDI2Y2YxOTEyN2U0ZjZjMGE1NWIyZTE2ZmExMjk4NDVhIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">185</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlBaWndvZ0hBejluTy9FdzA5bG54dHc9PSIsInZhbHVlIjoiU2pGUmtieFo0OGs1bzBWeGxnUWZyNDR0dUdOTjlUY1NBdVZvOGFBaUVxZ1BSRzIza2phbTdMT2I3UlVXaDJ1NmZ4bEdrc3BRVlFwSGVsVSt3WFlOTFJPbnErTEFRekJZVTkxUmxXV3JUcTA0S0w5UHEyV3pvRkFmNWpWOWVtcFoiLCJtYWMiOiI3MWQxNTYwNGYxZjU4MTIyYmMyYjk3YWNjYjkyMjFlMDI2Y2YxOTEyN2U0ZjZjMGE1NWIyZTE2ZmExMjk4NDVhIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6ImJaeXVIK3hRT2JMNUJpL2JGNjFuVmc9PSIsInZhbHVlIjoiSzFiMEU0Mk55RzJuQWY5b0x4K3M3V3ltdk1ESUdmSEtDaGFtQ2hYbDd4WDdqYkYrS0t3cTl3TWcxeUszNlJyWlhqaE1UVEQxam04U2tMUFl5YW1SeXc0YTdXR2pNYklUUTVhMmFCcTlWM2FxTUhyNTNrOC9nbWhHMWVFVjlmeUsiLCJtYWMiOiI3ODMxNTMwNGEzM2U3YjQ4YWY2MzMxOWY4YmRiMzljNzU5ZGQ3Y2FjMzgzMTZhOGIzNjFkNzdhYWUyMzQ1YzE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695795687\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-864941843 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864941843\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 16:04:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1438639839 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438639839\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/tables/bulk-actions", "action_name": "table.bulk-action.dispatch", "controller_action": "Botble\\Table\\Http\\Controllers\\TableBulkActionController@__invoke"}, "badge": null}}