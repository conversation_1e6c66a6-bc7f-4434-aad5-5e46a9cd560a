<?php

namespace Bo<PERSON>ble\Marketplace\Importers;

use Botble\DataSynchronize\Importer\ImportColumn;
use Bo<PERSON>ble\DataSynchronize\Importer\Importer;
use Botble\Ecommerce\Models\Customer;
use Botble\Marketplace\Enums\StoreStatusEnum;
use Botble\Marketplace\Facades\MarketplaceHelper;
use Botble\Marketplace\Models\Store;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreImporter extends Importer
{
    public function getLabel(): string
    {
        return trans('plugins/marketplace::store.stores');
    }

    public function columns(): array
    {
        return [
            ImportColumn::make('id')
                ->label('ID')
                ->rules(['nullable', 'integer'])
                ->nullable(),

            ImportColumn::make('name')
                ->label('Store Name')
                ->rules(['required', 'string', 'max:255']),

            ImportColumn::make('email')
                ->label('Email')
                ->rules(['required', 'email', 'max:255']),

            ImportColumn::make('phone')
                ->label('Phone')
                ->rules(['nullable', 'string', 'max:20']),

            ImportColumn::make('address')
                ->label('Address')
                ->rules(['nullable', 'string', 'max:500']),

            ImportColumn::make('country')
                ->label('Country')
                ->rules(['nullable', 'string', 'max:120']),

            ImportColumn::make('state')
                ->label('State')
                ->rules(['nullable', 'string', 'max:120']),

            ImportColumn::make('city')
                ->label('City')
                ->rules(['nullable', 'string', 'max:120']),

            ImportColumn::make('zip_code')
                ->label('Zip Code')
                ->rules(['nullable', 'string', 'max:20']),

            ImportColumn::make('company')
                ->label('Company')
                ->rules(['nullable', 'string', 'max:255']),

            ImportColumn::make('tax_id')
                ->label('Tax ID')
                ->rules(['nullable', 'string', 'max:255']),

            ImportColumn::make('description')
                ->label('Description')
                ->rules(['nullable', 'string', 'max:400']),

            ImportColumn::make('content')
                ->label('Content')
                ->rules(['nullable', 'string'])
                ->nullable(),

            ImportColumn::make('logo')
                ->label('Logo')
                ->rules(['nullable', 'string', 'max:255'])
                ->nullable(),

            ImportColumn::make('logo_square')
                ->label('Logo Square')
                ->rules(['nullable', 'string', 'max:255'])
                ->nullable(),

            ImportColumn::make('cover_image')
                ->label('Cover Image')
                ->rules(['nullable', 'string', 'max:255'])
                ->nullable(),

            ImportColumn::make('certificate_file')
                ->label('Certificate File')
                ->rules(['nullable', 'string', 'max:255'])
                ->nullable(),

            ImportColumn::make('government_id_file')
                ->label('Government ID File')
                ->rules(['nullable', 'string', 'max:255'])
                ->nullable(),

            ImportColumn::make('status')
                ->label('Status')
                ->rules(['nullable', Rule::in(StoreStatusEnum::values())])
                ->nullable(),

            ImportColumn::make('customer_id')
                ->label('Customer ID')
                ->rules(['nullable', 'integer', 'exists:ec_customers,id'])
                ->nullable(),

            ImportColumn::make('customer_email')
                ->label('Customer Email')
                ->rules(['nullable', 'email'])
                ->nullable(),
        ];
    }

    public function examples(): array
    {
        // Try to get real stores from the database for examples
        $stores = Store::with(['customer'])
            ->take(3)
            ->get();

        if ($stores->isNotEmpty()) {
            return $stores->map(function (Store $store) {
                return [
                    'id' => '', // Don't include ID to create new stores
                    'name' => $store->name,
                    'email' => $store->email . '.example', // Modify email to avoid conflicts
                    'phone' => $store->phone,
                    'address' => $store->address,
                    'country' => $store->country,
                    'state' => $store->state,
                    'city' => $store->city,
                    'zip_code' => $store->zip_code,
                    'company' => $store->company,
                    'tax_id' => $store->tax_id,
                    'description' => $store->description,
                    'content' => $store->content,
                    'logo' => $store->logo,
                    'logo_square' => $store->logo_square,
                    'cover_image' => $store->cover_image,
                    'certificate_file' => $store->certificate_file,
                    'government_id_file' => $store->government_id_file,
                    'status' => $store->status->getValue(),
                    'customer_id' => $store->customer_id,
                    'customer_email' => $store->customer->email ?? '',
                ];
            })->toArray();
        }

        // Fallback to hardcoded examples if no stores exist
        // But use real customer emails from the database
        $customers = Customer::take(2)->get();

        if ($customers->isEmpty()) {
            // If no customers exist, create a simple example that won't validate
            // This will prompt users to create customers first
            return [
                [
                    'name' => 'Example Store',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567890',
                    'address' => '123 Main Street',
                    'country' => 'United States',
                    'state' => 'California',
                    'city' => 'Los Angeles',
                    'zip_code' => '90210',
                    'company' => 'Example Company LLC',
                    'tax_id' => 'TAX123456',
                    'description' => 'This is an example store description',
                    'content' => 'Detailed content about the store',
                    'logo' => 'stores/logo.png',
                    'logo_square' => 'stores/logo-square.png',
                    'cover_image' => 'stores/cover.jpg',
                    'certificate_file' => 'stores/certificate.pdf',
                    'government_id_file' => 'stores/government-id.pdf',
                    'status' => StoreStatusEnum::PUBLISHED,
                    'customer_email' => '<EMAIL>',
                ],
            ];
        }

        return [
            [
                'name' => 'Example Store',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'address' => '123 Main Street',
                'country' => 'United States',
                'state' => 'California',
                'city' => 'Los Angeles',
                'zip_code' => '90210',
                'company' => 'Example Company LLC',
                'tax_id' => 'TAX123456',
                'description' => 'This is an example store description',
                'content' => 'Detailed content about the store',
                'logo' => 'stores/logo.png',
                'logo_square' => 'stores/logo-square.png',
                'cover_image' => 'stores/cover.jpg',
                'certificate_file' => 'stores/certificate.pdf',
                'government_id_file' => 'stores/government-id.pdf',
                'status' => StoreStatusEnum::PUBLISHED,
                'customer_email' => $customers->first()->email,
            ],
            [
                'name' => 'Another Store',
                'email' => '<EMAIL>',
                'phone' => '+0987654321',
                'address' => '456 Oak Avenue',
                'country' => 'Canada',
                'state' => 'Ontario',
                'city' => 'Toronto',
                'zip_code' => 'M5V 3A8',
                'company' => 'Another Company Inc',
                'tax_id' => 'TAX789012',
                'description' => 'Another example store',
                'content' => 'More detailed information',
                'logo' => 'stores/another-logo.png',
                'logo_square' => 'stores/another-logo-square.png',
                'cover_image' => 'stores/another-cover.jpg',
                'certificate_file' => null,
                'government_id_file' => null,
                'status' => StoreStatusEnum::PENDING,
                'customer_email' => $customers->count() > 1 ? $customers->get(1)->email : $customers->first()->email,
            ],
        ];
    }

    public function getLayout(): string
    {
        if (Auth::guard('customer')->check()) {
            return MarketplaceHelper::viewPath('vendor-dashboard.layouts.master');
        }

        return parent::getLayout();
    }

    public function getValidateUrl(): string
    {
        if (Auth::guard('customer')->check()) {
            return route('marketplace.vendor.import.stores.validate');
        }

        return route('marketplace.store.import.validate');
    }

    public function getImportUrl(): string
    {
        if (Auth::guard('customer')->check()) {
            return route('marketplace.vendor.import.stores.store');
        }

        return route('marketplace.store.import.store');
    }

    public function getDownloadExampleUrl(): ?string
    {
        if (Auth::guard('customer')->check()) {
            return route('marketplace.vendor.import.stores.download-example');
        }

        return route('marketplace.store.import.download-example');
    }

    public function getExportUrl(): ?string
    {
        if (Auth::guard('customer')->check()) {
            return route('marketplace.vendor.export.stores.index');
        }

        return route('marketplace.store.export.index');
    }

    public function getUploadUrl(): string
    {
        if (Auth::guard('customer')->check()) {
            return route('marketplace.vendor.import.data-synchronize.upload');
        }

        return route('data-synchronize.upload');
    }

    public function handle(array $data): int
    {
        $imported = 0;

        foreach ($data as $index => $row) {
            $this->currentRow = $index;
            $store = $this->processStoreData($row);

            if ($store) {
                $imported++;
            }
        }

        return $imported;
    }

    protected function processStoreData(array $row): ?Store
    {
        try {
            // Find customer by ID or email
            $customer = null;

            if (!empty($row['customer_id'])) {
                $customer = Customer::find($row['customer_id']);
            } elseif (!empty($row['customer_email'])) {
                $customer = Customer::where('email', $row['customer_email'])->first();
            }

            if (!$customer) {
                $this->onFailure($this->currentRow + 1, 'customer', ['Customer not found'], $row);
                return null;
            }

            // Check if store already exists
            $store = null;
            if (!empty($row['id'])) {
                $store = Store::find($row['id']);
            }

            if (!$store && !empty($row['email'])) {
                $store = Store::where('email', $row['email'])->first();
            }

            // Create new store if not exists
            if (!$store) {
                $store = new Store();
            }

            // Fill store data
            $store->fill([
                'name' => $row['name'],
                'email' => $row['email'],
                'phone' => $row['phone'] ?? null,
                'address' => $row['address'] ?? null,
                'country' => $row['country'] ?? null,
                'state' => $row['state'] ?? null,
                'city' => $row['city'] ?? null,
                'zip_code' => $row['zip_code'] ?? null,
                'company' => $row['company'] ?? null,
                'tax_id' => $row['tax_id'] ?? null,
                'description' => $row['description'] ?? null,
                'content' => $row['content'] ?? null,
                'logo' => $row['logo'] ?? null,
                'logo_square' => $row['logo_square'] ?? null,
                'cover_image' => $row['cover_image'] ?? null,
                'certificate_file' => $row['certificate_file'] ?? null,
                'government_id_file' => $row['government_id_file'] ?? null,
                'customer_id' => $customer->id,
                'status' => $row['status'] ?? StoreStatusEnum::PENDING,
            ]);

            $store->save();

            $this->onSuccess($row);
            return $store;
        } catch (\Exception $e) {
            $this->onFailure($this->currentRow + 1, 'general', [$e->getMessage()], $row);
            return null;
        }
    }
}
