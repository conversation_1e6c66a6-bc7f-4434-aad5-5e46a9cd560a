<?php

namespace Bo<PERSON>ble\Ecommerce\Enums;

use Bo<PERSON>ble\Base\Facades\Html;
use Botble\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static MainTypeEnum INDUSTRIAL()
 * @method static MainTypeEnum HEAVY_EQUIPMENT()
 * @method static MainTypeEnum MARINE()
 * @method static MainTypeEnum AEROSPACE()
 */
class MainTypeEnum extends Enum
{
    public const INDUSTRIAL = 'industrial';
    public const HEAVY_EQUIPMENT = 'heavy-equipment';
    public const MARINE = 'marine';
    public const AEROSPACE = 'aerospace';

    public static $langPath = 'plugins/ecommerce::products.main_types';

    public function toHtml(): HtmlString|string
    {
        return match ($this->value) {
            self::INDUSTRIAL => Html::tag('span', self::INDUSTRIAL()->label(), ['class' => 'text-success'])
                ->toHtml(),
            self::HEAVY_EQUIPMENT => Html::tag('span', self::HEAVY_EQUIPMENT()->label(), ['class' => 'text-info'])
                ->toHtml(),
            self::MARINE => Html::tag('span', self::MARINE()->label(), ['class' => 'text-info'])
                ->toHtml(),
            default => parent::toHtml(),
        };
    }
}
